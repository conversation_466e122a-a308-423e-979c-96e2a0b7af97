# WearLink Deployment Guide

## Quick Start

### 1. Environment Setup
```bash
# Clone the repository
git clone https://github.com/kenansabljakovic/wearlink.git
cd wearlink

# Copy environment template
cp .env.example .env

# Edit your credentials
nano .env
```

### 2. Configure Environment Variables
```bash
# Database Configuration
POSTGRES_DB=wearlink_data
POSTGRES_USER=wearlink_user
POSTGRES_PASSWORD=your_super_strong_password

# Garmin Connect Credentials
GARMIN_EMAIL=<EMAIL>
GARMIN_PASSWORD=your_garmin_password
```

### 3. Deploy the Platform
```bash
# Start all services
docker compose up -d

# Check service status
docker compose ps

# View logs
docker compose logs -f
```

### 4. Access the Dashboard
- **Dashboard**: http://localhost:3001
- **API Health**: http://localhost:3001/api/health
- **Database**: localhost:5433 (external access)

## Service Architecture

### Services Overview
- **wearlink-postgres**: PostgreSQL database with automatic schema initialization
- **garmin-data-sync**: Python service that syncs data from Garmin Connect
- **wearlink-frontend**: Next.js dashboard with API endpoints

### Service Dependencies
1. **Database** starts first and initializes schema
2. **Sync Service** waits for database health check, then starts syncing
3. **Frontend** waits for database health check, then serves dashboard

### Health Monitoring
All services include health checks:
- **Database**: `pg_isready` every 30 seconds
- **Sync Service**: Database connection test every 5 minutes
- **Frontend**: HTTP health endpoint every 30 seconds

## Development Workflow

### Local Development
```bash
# Start only database for development
docker compose up -d postgres

# Run frontend in development mode
cd frontend
npm install
npm run dev

# Run sync service manually (optional)
cd data-sync
pip install -r requirements.txt
python sync_garmin.py
```

### Production Deployment
```bash
# Build and start all services
docker compose up -d --build

# Check all services are healthy
docker compose ps

# Monitor logs
docker compose logs -f
```

## Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check service logs
docker compose logs [service-name]

# Restart specific service
docker compose restart [service-name]
```

#### Database Connection Issues
```bash
# Verify database is healthy
docker compose exec postgres pg_isready -U wearlink_user -d wearlink_data

# Check database tables
docker compose exec postgres psql -U wearlink_user -d wearlink_data -c "\dt"
```

#### Sync Service Issues
```bash
# Check Garmin credentials
docker compose logs garmin-data-sync

# Manual sync test
docker compose exec garmin-data-sync python -c "from garminconnect import Garmin; client = Garmin('email', 'password'); client.login()"
```

#### Frontend Issues
```bash
# Check API endpoints
curl http://localhost:3001/api/health
curl http://localhost:3001/api/stats/overview

# Check frontend logs
docker compose logs wearlink-frontend
```

### Performance Tuning

#### Database Optimization
- Increase shared_buffers for better performance
- Configure connection pooling for high traffic
- Monitor query performance with pg_stat_statements

#### Sync Service Optimization
- Adjust sync frequency based on usage
- Monitor API rate limits
- Configure retry logic for failed syncs

#### Frontend Optimization
- Enable compression for static assets
- Configure CDN for production deployments
- Monitor API response times

## Maintenance

### Backup and Recovery
```bash
# Backup database
docker compose exec postgres pg_dump -U wearlink_user wearlink_data > backup.sql

# Restore database
docker compose exec -T postgres psql -U wearlink_user wearlink_data < backup.sql
```

### Updates and Upgrades
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart services
docker compose up -d --build

# Check service health
docker compose ps
```

### Monitoring
- Check service logs regularly: `docker compose logs`
- Monitor database growth: `docker compose exec postgres du -sh /var/lib/postgresql/data`
- Monitor sync status via API: `curl http://localhost:3001/api/sync/status`

## Security Considerations

### Production Security
- Change default passwords in .env
- Use secure passwords (20+ characters)
- Restrict database port access
- Enable SSL/TLS for external access
- Regular security updates

### Network Security
- Use Docker networks for service isolation
- Limit external port exposure
- Configure firewall rules
- Monitor access logs