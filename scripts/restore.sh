#!/bin/bash

# WearLink Database Restore Script
# Restore database from backup with safety checks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if backup file is provided
if [[ $# -eq 0 ]]; then
    echo -e "${RED}❌ Usage: $0 <backup_file.sql.gz>${NC}"
    echo "Available backups:"
    ls -la /var/backups/wearlink/wearlink_backup_*.sql.gz 2>/dev/null || echo "No local backups found"
    exit 1
fi

BACKUP_FILE="$1"

# Check if backup file exists
if [[ ! -f "${BACKUP_FILE}" ]]; then
    echo -e "${RED}❌ Backup file not found: ${BACKUP_FILE}${NC}"
    exit 1
fi

# Load environment variables
source .env

echo -e "${GREEN}🔄 Starting WearLink database restore...${NC}"
echo -e "${YELLOW}⚠️  This will replace ALL data in the database!${NC}"

# Safety confirmation
read -p "Are you sure you want to continue? (yes/no): " -r
if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    echo "Restore cancelled"
    exit 1
fi

# Stop application services
echo "🛑 Stopping application services..."
docker compose stop wearlink-frontend garmin-data-sync

# Create a pre-restore backup
echo "💾 Creating pre-restore backup..."
PRERESTORE_BACKUP="/tmp/wearlink_prerestore_$(date +%Y%m%d_%H%M%S).sql"
docker compose exec -T postgres pg_dump \
    -U "${POSTGRES_USER}" \
    -d "${POSTGRES_DB}" > "${PRERESTORE_BACKUP}"
echo -e "${GREEN}✅ Pre-restore backup created: ${PRERESTORE_BACKUP}${NC}"

# Decompress backup if needed
TEMP_BACKUP="/tmp/restore_$(basename ${BACKUP_FILE})"
if [[ "${BACKUP_FILE}" == *.gz ]]; then
    echo "📦 Decompressing backup..."
    gunzip -c "${BACKUP_FILE}" > "${TEMP_BACKUP}"
else
    cp "${BACKUP_FILE}" "${TEMP_BACKUP}"
fi

# Restore database
echo "🗄️  Restoring database..."
docker compose exec -T postgres psql \
    -U "${POSTGRES_USER}" \
    -d "${POSTGRES_DB}" < "${TEMP_BACKUP}"

# Clean up temporary files
rm -f "${TEMP_BACKUP}"

# Restart services
echo "🚀 Restarting services..."
docker compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 15

# Verify restore
echo "🔍 Verifying restore..."
RECORD_COUNT=$(docker compose exec -T postgres psql \
    -U "${POSTGRES_USER}" \
    -d "${POSTGRES_DB}" \
    -t -c "SELECT COUNT(*) FROM daily_summaries;" | tr -d ' \n')

echo -e "${GREEN}✅ Restore completed successfully!${NC}"
echo "📊 Records in daily_summaries: ${RECORD_COUNT}"
echo "💾 Pre-restore backup saved at: ${PRERESTORE_BACKUP}"

# Test API endpoint
echo "🧪 Testing API endpoints..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/api/health | grep -q "200"; then
    echo -e "${GREEN}✅ API endpoints are responding${NC}"
else
    echo -e "${YELLOW}⚠️  API endpoints may not be ready yet${NC}"
fi

# Log restore to system log
logger "WearLink: Database restore completed from ${BACKUP_FILE}"

echo -e "${GREEN}🎉 Database restore process completed!${NC}"