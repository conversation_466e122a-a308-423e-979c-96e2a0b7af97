#!/bin/bash

# WearLink Database Backup Script
# Automated backup with S3 upload and retention management

set -e

# Load environment variables
source .env

# Configuration
BACKUP_DIR="/var/backups/wearlink"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILENAME="wearlink_backup_${TIMESTAMP}.sql"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_FILENAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🗄️  Starting WearLink database backup...${NC}"

# Create backup directory
mkdir -p "${BACKUP_DIR}"

# Create database backup
echo "📦 Creating database dump..."
docker compose exec -T postgres pg_dump \
    -U "${POSTGRES_USER}" \
    -d "${POSTGRES_DB}" \
    --verbose \
    --no-owner \
    --no-privileges \
    --clean \
    --if-exists > "${BACKUP_PATH}"

# Compress backup
echo "🗜️  Compressing backup..."
gzip "${BACKUP_PATH}"
BACKUP_PATH="${BACKUP_PATH}.gz"

# Calculate backup size
BACKUP_SIZE=$(du -h "${BACKUP_PATH}" | cut -f1)
echo -e "${GREEN}✅ Backup created: ${BACKUP_FILENAME}.gz (${BACKUP_SIZE})${NC}"

# Upload to S3 if configured
if [[ "${BACKUP_S3_BUCKET}" != "" && "${AWS_ACCESS_KEY_ID}" != "" ]]; then
    echo "☁️  Uploading to S3..."
    
    if command -v aws &> /dev/null; then
        aws s3 cp "${BACKUP_PATH}" "s3://${BACKUP_S3_BUCKET}/database/" \
            --storage-class STANDARD_IA
        echo -e "${GREEN}✅ Backup uploaded to S3${NC}"
    else
        echo -e "${YELLOW}⚠️  AWS CLI not available, skipping S3 upload${NC}"
    fi
fi

# Clean up old local backups
echo "🧹 Cleaning up old backups..."
find "${BACKUP_DIR}" -name "wearlink_backup_*.sql.gz" -mtime +${BACKUP_RETENTION_DAYS:-7} -delete

# Clean up old S3 backups if configured
if [[ "${BACKUP_S3_BUCKET}" != "" && "${AWS_ACCESS_KEY_ID}" != "" ]]; then
    if command -v aws &> /dev/null; then
        # List and delete old S3 backups
        OLD_DATE=$(date -d "${BACKUP_RETENTION_DAYS:-30} days ago" +%Y%m%d)
        aws s3 ls "s3://${BACKUP_S3_BUCKET}/database/" | while read -r line; do
            BACKUP_DATE=$(echo $line | awk '{print $4}' | grep -o '[0-9]\{8\}' | head -1)
            if [[ "${BACKUP_DATE}" < "${OLD_DATE}" ]]; then
                BACKUP_KEY=$(echo $line | awk '{print $4}')
                aws s3 rm "s3://${BACKUP_S3_BUCKET}/database/${BACKUP_KEY}"
                echo "🗑️  Deleted old S3 backup: ${BACKUP_KEY}"
            fi
        done
    fi
fi

# Log backup to system log
logger "WearLink: Database backup completed successfully (${BACKUP_SIZE})"

echo -e "${GREEN}🎉 Backup process completed successfully!${NC}"

# Optional: Send notification (Slack, email, etc.)
if [[ "${BACKUP_NOTIFICATION_WEBHOOK}" != "" ]]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"✅ WearLink backup completed: ${BACKUP_FILENAME}.gz (${BACKUP_SIZE})\"}" \
        "${BACKUP_NOTIFICATION_WEBHOOK}" || true
fi