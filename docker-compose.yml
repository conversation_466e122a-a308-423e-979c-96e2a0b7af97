version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: wearlink-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-wearlink_data}
      POSTGRES_USER: ${POSTGRES_USER:-wearlink_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_super_strong_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5433:5432"  # Avoid conflicts with system PostgreSQL
    networks:
      - wearlink_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-wearlink_user} -d ${POSTGRES_DB:-wearlink_data}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Garmin Data Sync Service
  garmin-data-sync:
    build:
      context: ./data-sync
      dockerfile: Dockerfile
    container_name: garmin-data-sync
    restart: unless-stopped
    environment:
      # Database connection
      DB_HOST: postgres
      DB_NAME: ${POSTGRES_DB:-wearlink_data}
      DB_USER: ${POSTGRES_USER:-wearlink_user}
      DB_PASS: ${POSTGRES_PASSWORD:-your_super_strong_password}
      # Garmin Connect credentials
      GARMIN_EMAIL: ${GARMIN_EMAIL}
      GARMIN_PASSWORD: ${GARMIN_PASSWORD}
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - wearlink_network
    volumes:
      - ./data-sync:/app:ro  # Mount for development
    healthcheck:
      test: ["CMD", "python", "-c", "import psycopg2; psycopg2.connect(host='postgres', dbname='${POSTGRES_DB:-wearlink_data}', user='${POSTGRES_USER:-wearlink_user}', password='${POSTGRES_PASSWORD:-your_super_strong_password}')"]
      interval: 300s  # Check every 5 minutes
      timeout: 30s
      retries: 3
      start_period: 60s

  # Next.js Frontend Dashboard
  wearlink-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: wearlink-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER:-wearlink_user}:${POSTGRES_PASSWORD:-your_super_strong_password}@postgres:5432/${POSTGRES_DB:-wearlink_data}
    ports:
      - "3001:3001"  # Match package.json start script
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - wearlink_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  wearlink_network:
    driver: bridge
    name: wearlink_fitness_network

volumes:
  postgres_data:
    name: wearlink_postgres_data