version: '3.8'

services:
  # PostgreSQL Database - Production Configuration
  postgres:
    image: postgres:15-alpine
    container_name: wearlink-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - /var/log/wearlink:/var/log/postgresql
    ports:
      - "127.0.0.1:5433:5432"  # Bind to localhost only
    networks:
      - wearlink_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    command: >
      postgres
      -c max_connections=${DATABASE_MAX_CONNECTIONS:-100}
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c log_destination=stderr
      -c logging_collector=on
      -c log_directory=/var/log/postgresql
      -c log_filename='postgresql-%Y-%m-%d_%H%M%S.log'
      -c log_statement=mod
      -c log_min_duration_statement=1000

  # Garmin Data Sync Service - Production Configuration
  garmin-data-sync:
    build:
      context: ./data-sync
      dockerfile: Dockerfile.prod
    container_name: wearlink-sync-prod
    restart: unless-stopped
    environment:
      # Database connection
      DB_HOST: postgres
      DB_NAME: ${POSTGRES_DB}
      DB_USER: ${POSTGRES_USER}
      DB_PASS: ${POSTGRES_PASSWORD}
      # Garmin Connect credentials
      GARMIN_EMAIL: ${GARMIN_EMAIL}
      GARMIN_PASSWORD: ${GARMIN_PASSWORD}
      # Production settings
      LOG_LEVEL: ${LOG_LEVEL:-info}
      SYNC_INTERVAL_HOURS: ${SYNC_INTERVAL_HOURS:-1}
      SYNC_RETRY_ATTEMPTS: ${SYNC_RETRY_ATTEMPTS:-5}
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - wearlink_network
    volumes:
      - /var/log/wearlink:/var/log/wearlink
    healthcheck:
      test: ["CMD", "python", "-c", "import psycopg2; psycopg2.connect(host='postgres', dbname='${POSTGRES_DB}', user='${POSTGRES_USER}', password='${POSTGRES_PASSWORD}')"]
      interval: 300s
      timeout: 30s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.1'

  # Next.js Frontend - Production Configuration
  wearlink-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: wearlink-frontend-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      SESSION_SECRET: ${SESSION_SECRET}
      JWT_SECRET: ${JWT_SECRET}
      CORS_ORIGIN: ${CORS_ORIGIN}
    ports:
      - "${APP_PORT:-3001}:3001"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - wearlink_network
    volumes:
      - /var/log/wearlink:/var/log/wearlink
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: wearlink-redis-prod
    restart: unless-stopped
    profiles:
      - cache
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - wearlink_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    command: redis-server --requirepass ${REDIS_PASSWORD:-default_password} --maxmemory 200mb --maxmemory-policy allkeys-lru

  # Prometheus Metrics (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: wearlink-prometheus-prod
    restart: unless-stopped
    profiles:
      - monitoring
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "127.0.0.1:9090:9090"
    networks:
      - wearlink_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  # Grafana Dashboard (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: wearlink-grafana-prod
    restart: unless-stopped
    profiles:
      - monitoring
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    ports:
      - "127.0.0.1:3000:3000"
    networks:
      - wearlink_network

networks:
  wearlink_network:
    driver: bridge
    name: wearlink_production_network

volumes:
  postgres_data:
    name: wearlink_postgres_production_data
  redis_data:
    name: wearlink_redis_production_data
  prometheus_data:
    name: wearlink_prometheus_data
  grafana_data:
    name: wearlink_grafana_data