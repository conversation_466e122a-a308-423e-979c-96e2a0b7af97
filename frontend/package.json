{"name": "wearlink", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -H 0.0.0.0 -p 3001", "dev:local": "next dev --turbopack", "build": "next build", "start": "next start -H 0.0.0.0 -p 3001", "lint": "next lint"}, "dependencies": {"@types/pg": "^8.15.4", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "15.4.2", "pg": "^8.16.3", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}