"use client";

import {
  BarChart3,
  TrendingUp,
  Calendar,
  User,
  Menu,
  X,
  Activity,
} from "lucide-react";
import { useClientOnly } from "../hooks/useClientOnly";

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  collapsed: boolean;
  onToggleCollapsed: () => void;
}

export default function Sidebar({
  activeTab,
  onTabChange,
  collapsed,
  onToggleCollapsed,
}: SidebarProps) {
  const mounted = useClientOnly();

  const navigationItems = [
    { id: "overview", label: "Overview", icon: BarChart3, active: true },
    { id: "trends", label: "Trends", icon: TrendingUp, active: false },
    { id: "activities", label: "Activities", icon: Calendar, active: false },
  ];

  // Prevent hydration mismatch by only rendering after client mount
  if (!mounted) {
    return (
      <aside className="fixed left-0 top-0 h-full w-72 bg-slate-900 border-r border-slate-700 z-40">
        <div className="flex flex-col h-full">
          <div className="p-6 border-b border-slate-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-violet-500 rounded-lg flex items-center justify-center">
                <Activity className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-white">Fitness</h1>
                <h2 className="text-lg font-bold text-white">Analytics</h2>
                <p className="text-sm text-slate-400 mt-1">
                  Your health dashboard
                </p>
              </div>
            </div>
          </div>
        </div>
      </aside>
    );
  }

  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={onToggleCollapsed}
        className="md:hidden fixed top-4 left-4 z-50 p-2 rounded-lg bg-slate-800 text-white"
        suppressHydrationWarning
      >
        {collapsed ? <Menu size={20} /> : <X size={20} />}
      </button>

      {/* Sidebar */}
      <aside
        className={`fixed left-0 top-0 h-full bg-slate-900 border-r border-slate-700 transition-all duration-300 z-40 ${
          collapsed ? "-translate-x-full md:translate-x-0 md:w-20" : "w-72"
        }`}
        suppressHydrationWarning
      >
        <div className="flex flex-col h-full">
          {/* Logo/Brand Section */}
          <div className="p-6 border-b border-slate-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-violet-500 rounded-lg flex items-center justify-center">
                <Activity className="w-6 h-6 text-white" />
              </div>
              {!collapsed && (
                <div>
                  <h1 className="text-lg font-bold text-white">Fitness</h1>
                  <h2 className="text-lg font-bold text-white">Analytics</h2>
                  <p className="text-sm text-slate-400 mt-1">
                    Your health dashboard
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* User Profile Section */}
          {!collapsed && (
            <div className="px-6 py-4 border-b border-slate-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-violet-500 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-white font-medium text-sm">Alex Chen</h3>
                  <p className="text-xs text-slate-400">Premium Member</p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Menu */}
          <nav className="flex-1 px-6 py-4">
            <ul className="space-y-1">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeTab === item.id;

                return (
                  <li key={item.id}>
                    <button
                      onClick={() => onTabChange(item.id)}
                      className={`w-full px-2 py-2 rounded-lg transition-all duration-200 flex items-center ${
                        collapsed ? "justify-center" : "justify-start"
                      } ${
                        isActive
                          ? "text-white"
                          : "text-slate-400 hover:text-slate-200"
                      }`}
                    >
                      {collapsed ? (
                        <Icon
                          className={`w-5 h-5 ${
                            isActive ? "text-violet-400" : ""
                          }`}
                        />
                      ) : (
                        <div className="flex items-center w-full">
                          {/* Compact active pill */}
                          <div
                            className={`inline-flex items-center gap-3 ${
                              isActive
                                ? "bg-violet-500/90 text-white px-3 py-2 rounded-full"
                                : ""
                            }`}
                          >
                            <Icon className="w-5 h-5" />
                            <span className="font-medium">{item.label}</span>
                          </div>
                          {/* Right indicator dot */}
                          {isActive && (
                            <span className="ml-auto w-2 h-2 bg-white rounded-full" />
                          )}
                        </div>
                      )}
                    </button>
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* Footer/Settings */}
          {!collapsed && (
            <div className="p-6 border-t border-slate-700">
              <p className="text-xs text-slate-500 text-center">
                WearLink v1.0 • Analytics Dashboard
              </p>
            </div>
          )}
        </div>
      </aside>

      {/* Mobile overlay */}
      {!collapsed && (
        <div
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={onToggleCollapsed}
          suppressHydrationWarning
        />
      )}
    </>
  );
}
