'use client';

import { useClientOnly } from '../hooks/useClientOnly';

interface ClientOnlyChartProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export default function ClientOnlyChart({ children, fallback }: ClientOnlyChartProps) {
  const mounted = useClientOnly();

  if (!mounted) {
    return (
      <div className="chart-container p-6 h-80">
        {fallback || (
          <div className="h-64 flex items-center justify-center">
            <div className="text-slate-400">Loading chart...</div>
          </div>
        )}
      </div>
    );
  }

  return <>{children}</>;
}