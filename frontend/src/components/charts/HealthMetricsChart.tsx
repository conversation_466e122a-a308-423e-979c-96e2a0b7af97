'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, BarChart, Bar, Area, AreaChart } from 'recharts';
import { format, parseISO } from 'date-fns';
import { useClientOnly } from '../../hooks/useClientOnly';
import { Heart, Activity, Battery, Moon, TrendingUp, TrendingDown } from 'lucide-react';

interface HealthTrendsData {
  daily_data: {
    date: string;
    min_hr: number;
    max_hr: number;
    avg_hr: number;
    stress_level: number;
    body_battery: number;
    sleep_hours: number;
  }[];
  weekly_data: {
    week_start: string;
    week_label: string;
    avg_min_hr: number;
    avg_max_hr: number;
    avg_stress: number;
    avg_body_battery: number;
    avg_sleep: number;
    days_count: number;
  }[];
  current_averages: {
    min_hr: number;
    max_hr: number;
    avg_hr: number;
    stress_level: number;
    body_battery: number;
    sleep_hours: number;
  };
  trends: {
    min_hr_change: number;
    max_hr_change: number;
    stress_change: number;
    body_battery_change: number;
    sleep_change: number;
  };
}

interface HealthMetricsChartProps {
  className?: string;
}

export default function HealthMetricsChart({ className = '' }: HealthMetricsChartProps) {
  const [data, setData] = useState<HealthTrendsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'heart_rate' | 'stress' | 'sleep'>('overview');
  const [timeRange, setTimeRange] = useState('30');
  const [dimensions, setDimensions] = useState({ width: 800, height: 280 });
  const mounted = useClientOnly();

  // Update dimensions based on screen size
  useEffect(() => {
    if (!mounted) return;
    
    const updateDimensions = () => {
      const screenWidth = window.innerWidth;
      if (screenWidth < 768) {
        setDimensions({ width: 350, height: 240 });
      } else if (screenWidth < 1024) {
        setDimensions({ width: 500, height: 260 });
      } else {
        setDimensions({ width: 800, height: 280 });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [mounted]);

  useEffect(() => {
    if (!mounted) return;

    async function fetchHealthTrends() {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/health/trends?days=${timeRange}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch health trends: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.success && result.data) {
          setData(result.data);
        } else {
          throw new Error(result.error || 'Invalid data format');
        }
      } catch (err) {
        console.error('Error fetching health trends:', err);
        setError(err instanceof Error ? err.message : 'Failed to load health data');
      } finally {
        setLoading(false);
      }
    }

    fetchHealthTrends();
  }, [timeRange, mounted]);

  // Don't render until mounted (client-side)
  if (!mounted) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <h3 className="text-lg font-semibold text-slate-50 mb-4">Health Metrics</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-slate-400">Loading chart...</div>
        </div>
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-slate-800 border border-slate-700 rounded-lg p-3 shadow-lg">
          <p className="text-slate-200 text-sm font-medium mb-2">
            {activeView === 'overview' ? label : format(parseISO(label), 'MMM dd, yyyy')}
          </p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'min_hr' && `Resting HR: ${entry.value} bpm`}
              {entry.dataKey === 'max_hr' && `Max HR: ${entry.value} bpm`}
              {entry.dataKey === 'avg_hr' && `Avg HR: ${entry.value} bpm`}
              {entry.dataKey === 'stress_level' && `Stress: ${entry.value}/100`}
              {entry.dataKey === 'avg_stress' && `Avg Stress: ${entry.value}/100`}
              {entry.dataKey === 'body_battery' && `Body Battery: ${entry.value}%`}
              {entry.dataKey === 'avg_body_battery' && `Body Battery: ${entry.value}%`}
              {entry.dataKey === 'sleep_hours' && `Sleep: ${entry.value}h`}
              {entry.dataKey === 'avg_sleep' && `Sleep: ${entry.value}h`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const TrendIndicator = ({ value, label, icon: Icon, unit = '' }: { 
    value: number, 
    label: string, 
    icon: any,
    unit?: string 
  }) => {
    const isPositive = value > 0;
    const TrendIcon = isPositive ? TrendingUp : TrendingDown;
    const isGoodTrend = (label === 'Sleep' && isPositive) || 
                       (label === 'Body Battery' && isPositive) ||
                       (label === 'Stress' && !isPositive);
    
    return (
      <div className="flex items-center space-x-3 p-3 bg-slate-800 rounded-lg">
        <Icon className="w-5 h-5 text-slate-400" />
        <div className="flex-1">
          <p className="text-slate-300 text-sm font-medium">{label}</p>
          <div className={`flex items-center space-x-1 text-sm ${
            isGoodTrend ? 'text-green-400' : !isPositive && label !== 'Stress' ? 'text-red-400' : 'text-yellow-400'
          }`}>
            <TrendIcon className="w-3 h-3" />
            <span>
              {isPositive ? '+' : ''}{Math.abs(value).toFixed(1)}{unit}
            </span>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-slate-50">Health Metrics</h3>
          <div className="flex space-x-2">
            {['Overview', 'Heart Rate', 'Stress', 'Sleep'].map((view) => (
              <div key={view} className="w-16 h-6 bg-slate-700 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
        <div className="h-64 flex items-center justify-center">
          <div className="text-slate-400">Loading health data...</div>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <h3 className="text-lg font-semibold text-slate-50 mb-4">Health Metrics</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-red-400">Error: {error || 'No data available'}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`chart-container p-6 h-80 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-slate-50">Health Metrics</h3>
          <p className="text-sm text-slate-400">
            {timeRange === '7' ? 'Last 7 days' :
             timeRange === '30' ? 'Last 30 days' :
             timeRange === '90' ? 'Last 3 months' : 'Health trends'}
          </p>
        </div>
        <div className="flex space-x-2">
          {[
            { key: 'overview', label: 'Overview' },
            { key: 'heart_rate', label: 'Heart Rate' },
            { key: 'stress', label: 'Stress' },
            { key: 'sleep', label: 'Sleep' }
          ].map((view) => (
            <button
              key={view.key}
              onClick={() => setActiveView(view.key as any)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                activeView === view.key
                  ? 'bg-violet-500 text-white'
                  : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800'
              }`}
            >
              {view.label}
            </button>
          ))}
        </div>
      </div>
      
      <div className="h-64">
        {/* Overview: Current metrics with trends */}
        {activeView === 'overview' && (
          <div className="h-full">
            <div className="grid grid-cols-2 gap-4 h-full">
              {/* Current Averages */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-slate-300 mb-3">Current Period</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Heart className="w-4 h-4 text-red-400" />
                      <span className="text-slate-300 text-sm">Avg Heart Rate</span>
                    </div>
                    <span className="text-slate-50 font-semibold">{data?.current_averages?.avg_hr || 0} bpm</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-yellow-400" />
                      <span className="text-slate-300 text-sm">Stress Level</span>
                    </div>
                    <span className="text-slate-50 font-semibold">{data?.current_averages?.stress_level || 0}/100</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Battery className="w-4 h-4 text-green-400" />
                      <span className="text-slate-300 text-sm">Body Battery</span>
                    </div>
                    <span className="text-slate-50 font-semibold">{data?.current_averages?.body_battery || 0}%</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Moon className="w-4 h-4 text-blue-400" />
                      <span className="text-slate-300 text-sm">Sleep</span>
                    </div>
                    <span className="text-slate-50 font-semibold">{data?.current_averages?.sleep_hours || 0}h</span>
                  </div>
                </div>
              </div>
              
              {/* Period-over-Period Trends */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-slate-300 mb-3">Trends vs Previous Period</h4>
                <div className="space-y-2">
                  <TrendIndicator 
                    value={data?.trends?.min_hr_change || 0} 
                    label="Resting HR" 
                    icon={Heart}
                    unit="%" 
                  />
                  <TrendIndicator 
                    value={data?.trends?.stress_change || 0} 
                    label="Stress" 
                    icon={Activity}
                    unit="%" 
                  />
                  <TrendIndicator 
                    value={data?.trends?.body_battery_change || 0} 
                    label="Body Battery" 
                    icon={Battery}
                    unit="%" 
                  />
                  <TrendIndicator 
                    value={data?.trends?.sleep_change || 0} 
                    label="Sleep" 
                    icon={Moon}
                    unit="%" 
                  />
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Heart Rate: Multi-line chart */}
        {activeView === 'heart_rate' && (
          <div className="h-full flex items-center justify-center">
            {data.daily_data.length > 0 ? (
              <div style={{ width: `${dimensions.width}px`, height: `${dimensions.height}px` }}>
                <LineChart width={dimensions.width} height={dimensions.height} data={data.daily_data}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" opacity={0.3} />
                  <XAxis 
                    dataKey="date" 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                    tickFormatter={(value) => format(parseISO(value), 'MMM dd')}
                  />
                  <YAxis 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                    domain={['dataMin - 5', 'dataMax + 5']}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line 
                    type="monotone" 
                    dataKey="min_hr" 
                    stroke="#10b981" 
                    strokeWidth={2}
                    dot={{ r: 2 }}
                    name="Resting HR"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="max_hr" 
                    stroke="#ef4444" 
                    strokeWidth={2}
                    dot={{ r: 2 }}
                    name="Max HR"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="avg_hr" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    dot={{ r: 2 }}
                    name="Average HR"
                  />
                </LineChart>
              </div>
            ) : (
              <div className="text-slate-400">No heart rate data available</div>
            )}
          </div>
        )}
        
        {/* Stress: Area chart */}
        {activeView === 'stress' && (
          <div className="h-full flex items-center justify-center">
            {data.daily_data.length > 0 ? (
              <div style={{ width: `${dimensions.width}px`, height: `${dimensions.height}px` }}>
                <AreaChart width={dimensions.width} height={dimensions.height} data={data.daily_data}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" opacity={0.3} />
                  <XAxis 
                    dataKey="date" 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                    tickFormatter={(value) => format(parseISO(value), 'MMM dd')}
                  />
                  <YAxis 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                    domain={[0, 100]}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area 
                    type="monotone" 
                    dataKey="stress_level" 
                    stroke="#f59e0b" 
                    fill="#f59e0b"
                    fillOpacity={0.3}
                    strokeWidth={2}
                  />
                </AreaChart>
              </div>
            ) : (
              <div className="text-slate-400">No stress data available</div>
            )}
          </div>
        )}
        
        {/* Sleep: Bar chart */}
        {activeView === 'sleep' && (
          <div className="h-full flex items-center justify-center">
            {data.daily_data.length > 0 ? (
              <div style={{ width: `${dimensions.width}px`, height: `${dimensions.height}px` }}>
                <BarChart width={dimensions.width} height={dimensions.height} data={data.daily_data}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" opacity={0.3} />
                  <XAxis 
                    dataKey="date" 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                    tickFormatter={(value) => format(parseISO(value), 'MMM dd')}
                  />
                  <YAxis 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                    domain={[0, 12]}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar 
                    dataKey="sleep_hours" 
                    fill="#3b82f6" 
                    radius={[2, 2, 0, 0]}
                  />
                </BarChart>
              </div>
            ) : (
              <div className="text-slate-400">No sleep data available</div>
            )}
          </div>
        )}
      </div>
      
      {/* Time range selector */}
      <div className="flex items-center justify-center mt-4 space-x-2">
        {[
          { label: '7D', value: '7' },
          { label: '30D', value: '30' },
          { label: '90D', value: '90' }
        ].map((range) => (
          <button
            key={range.value}
            onClick={() => setTimeRange(range.value)}
            className={`px-3 py-1 text-xs font-medium rounded-md transition-all duration-200 ${
              timeRange === range.value
                ? 'bg-slate-700 text-slate-200'
                : 'text-slate-500 hover:text-slate-300 hover:bg-slate-800'
            }`}
          >
            {range.label}
          </button>
        ))}
      </div>
    </div>
  );
}