'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, LineChart, Line, PieChart, Pie, Cell, Area, AreaChart } from 'recharts';
import { format, parseISO } from 'date-fns';
import { useClientOnly } from '../../hooks/useClientOnly';
import { Trophy, TrendingUp, TrendingDown, Timer, Gauge, Target, Zap } from 'lucide-react';

interface PerformanceData {
  performance_summary: {
    total_activities: number;
    total_distance: number;
    total_duration_hours: number;
    total_training_load: number;
    avg_distance: number;
    avg_pace: number;
    avg_heart_rate: number;
  };
  activity_type_breakdown: Array<{
    activity_type: string;
    count: number;
    total_distance: number;
    avg_pace: number;
    avg_heart_rate: number;
  }>;
  training_zones: {
    zones: Record<string, { min: number; max: number; name: string }>;
    distribution: Record<string, number>;
    max_hr: number;
  };
  weekly_progression: Array<{
    week_start: string;
    week_label: string;
    activity_count: number;
    total_distance: number;
    total_training_load: number;
    avg_pace: number;
    avg_hr: number;
  }>;
  personal_records: {
    fastest_5k: number;
    longest_distance: number;
    longest_duration: number;
    highest_training_load: number;
  };
}

interface PerformanceAnalyticsChartProps {
  className?: string;
}

// Training zone colors
const ZONE_COLORS: Record<string, string> = {
  zone1: '#10b981', // Green - Recovery
  zone2: '#3b82f6', // Blue - Aerobic
  zone3: '#f59e0b', // Yellow - Tempo  
  zone4: '#ef4444', // Red - Threshold
  zone5: '#8b5cf6', // Purple - VO2 Max
};

export default function PerformanceAnalyticsChart({ className = '' }: PerformanceAnalyticsChartProps) {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'training_load' | 'pace_trends' | 'zones'>('overview');
  const [timeRange, setTimeRange] = useState('90');
  const [dimensions, setDimensions] = useState({ width: 800, height: 280 });
  const mounted = useClientOnly();

  // Update dimensions based on screen size
  useEffect(() => {
    if (!mounted) return;
    
    const updateDimensions = () => {
      const screenWidth = window.innerWidth;
      if (screenWidth < 768) {
        setDimensions({ width: 350, height: 240 });
      } else if (screenWidth < 1024) {
        setDimensions({ width: 500, height: 260 });
      } else {
        setDimensions({ width: 800, height: 280 });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [mounted]);

  useEffect(() => {
    if (!mounted) return;

    async function fetchPerformanceAnalytics() {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/performance/analytics?days=${timeRange}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch performance analytics: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.success && result.data) {
          setData(result.data);
        } else {
          throw new Error(result.error || 'Invalid data format');
        }
      } catch (err) {
        console.error('Error fetching performance analytics:', err);
        setError(err instanceof Error ? err.message : 'Failed to load performance data');
      } finally {
        setLoading(false);
      }
    }

    fetchPerformanceAnalytics();
  }, [timeRange, mounted]);

  // Don't render until mounted (client-side)
  if (!mounted) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <h3 className="text-lg font-semibold text-slate-50 mb-4">Performance Analytics</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-slate-400">Loading chart...</div>
        </div>
      </div>
    );
  }

  const formatPace = (pace: number) => {
    if (!pace) return '--:--';
    const minutes = Math.floor(pace);
    const seconds = Math.round((pace - minutes) * 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-slate-800 border border-slate-700 rounded-lg p-3 shadow-lg">
          <p className="text-slate-200 text-sm font-medium mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'total_distance' && `Distance: ${entry.value.toFixed(1)} km`}
              {entry.dataKey === 'total_training_load' && `Training Load: ${entry.value.toFixed(0)}`}
              {entry.dataKey === 'activity_count' && `Activities: ${entry.value}`}
              {entry.dataKey === 'avg_pace' && `Avg Pace: ${formatPace(entry.value)}`}
              {entry.dataKey === 'avg_hr' && `Avg HR: ${entry.value?.toFixed(0)} bpm`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const StatCard = ({ 
    title, 
    value, 
    unit = '',
    icon: Icon, 
    trend,
    color = 'blue' 
  }: {
    title: string;
    value: number | string;
    unit?: string;
    icon: React.ComponentType<{ className?: string }>;
    trend?: number;
    color?: 'blue' | 'green' | 'orange' | 'red' | 'purple';
  }) => {
    const colorClasses = {
      blue: 'bg-blue-500/10 border-blue-500/20 text-blue-400',
      green: 'bg-emerald-500/10 border-emerald-500/20 text-emerald-400',
      orange: 'bg-amber-500/10 border-amber-500/20 text-amber-400',
      red: 'bg-red-500/10 border-red-500/20 text-red-400',
      purple: 'bg-violet-500/10 border-violet-500/20 text-violet-400',
    };

    return (
      <div className={`p-4 rounded-lg ${colorClasses[color]}`}>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-slate-400 mb-1">{title}</p>
            <p className="text-xl font-bold text-slate-50">
              {typeof value === 'number' ? value.toLocaleString() : value}{unit}
            </p>
            {trend !== undefined && (
              <div className="flex items-center mt-1">
                {trend > 0 ? (
                  <TrendingUp className="w-3 h-3 text-emerald-400 mr-1" />
                ) : trend < 0 ? (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                ) : null}
                <span className={`text-xs ${
                  trend > 0 ? 'text-emerald-400' : trend < 0 ? 'text-red-400' : 'text-slate-400'
                }`}>
                  {trend > 0 ? '+' : ''}{trend}%
                </span>
              </div>
            )}
          </div>
          <Icon className={`w-5 h-5 ${colorClasses[color].split(' ')[3]}`} />
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-slate-50">Performance Analytics</h3>
          <div className="flex space-x-2">
            {['Overview', 'Training Load', 'Pace Trends', 'HR Zones'].map((view) => (
              <div key={view} className="w-16 h-6 bg-slate-700 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
        <div className="h-64 flex items-center justify-center">
          <div className="text-slate-400">Loading performance data...</div>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <h3 className="text-lg font-semibold text-slate-50 mb-4">Performance Analytics</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-red-400">Error: {error || 'No data available'}</div>
        </div>
      </div>
    );
  }

  // Prepare zone distribution data for pie chart
  const zoneData = Object.entries(data.training_zones.distribution).map(([zone, count]) => ({
    name: data.training_zones.zones[zone]?.name || zone,
    value: count,
    color: ZONE_COLORS[zone] || '#6b7280'
  }));

  return (
    <div className={`chart-container p-6 h-80 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-slate-50">Performance Analytics</h3>
          <p className="text-sm text-slate-400">
            {timeRange === '30' ? 'Last 30 days' :
             timeRange === '90' ? 'Last 3 months' :
             timeRange === '180' ? 'Last 6 months' : 'Performance analysis'}
          </p>
        </div>
        <div className="flex space-x-2">
          {[
            { key: 'overview', label: 'Overview' },
            { key: 'training_load', label: 'Training Load' },
            { key: 'pace_trends', label: 'Pace Trends' },
            { key: 'zones', label: 'HR Zones' }
          ].map((view) => (
            <button
              key={view.key}
              onClick={() => setActiveView(view.key as any)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                activeView === view.key
                  ? 'bg-violet-500 text-white'
                  : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800'
              }`}
            >
              {view.label}
            </button>
          ))}
        </div>
      </div>
      
      <div className="h-64">
        {/* Overview: Performance summary cards */}
        {activeView === 'overview' && (
          <div className="h-full">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 h-full">
              <StatCard
                title="Total Activities"
                value={data.performance_summary.total_activities}
                icon={Target}
                color="blue"
              />
              <StatCard
                title="Total Distance"
                value={data.performance_summary.total_distance.toFixed(1)}
                unit=" km"
                icon={Trophy}
                color="green"
              />
              <StatCard
                title="Avg Pace"
                value={data.performance_summary.avg_pace ? formatPace(data.performance_summary.avg_pace) : '---'}
                unit="/km"
                icon={Timer}
                color="orange"
              />
              <StatCard
                title="Training Load"
                value={data.performance_summary.total_training_load}
                icon={Zap}
                color="purple"
              />
              <StatCard
                title="Longest Run"
                value={data.personal_records.longest_distance.toFixed(1)}
                unit=" km"
                icon={Trophy}
                color="blue"
              />
              <StatCard
                title="Best 5K Pace"
                value={data.personal_records.fastest_5k ? formatPace(data.personal_records.fastest_5k) : '---'}
                unit="/km"
                icon={Gauge}
                color="red"
              />
              <StatCard
                title="Avg Heart Rate"
                value={data.performance_summary.avg_heart_rate ? data.performance_summary.avg_heart_rate.toFixed(0) : '---'}
                unit=" bpm"
                icon={Timer}
                color="red"
              />
              <StatCard
                title="Weekly Volume"
                value={data.weekly_progression.length > 0 ? 
                  (data.weekly_progression[data.weekly_progression.length - 1]?.total_distance?.toFixed(1) || '0') : '0'}
                unit=" km"
                icon={TrendingUp}
                color="green"
              />
            </div>
          </div>
        )}
        
        {/* Training Load: Weekly training load progression */}
        {activeView === 'training_load' && (
          <div className="h-full flex items-center justify-center">
            {data.weekly_progression.length > 0 ? (
              <div style={{ width: `${dimensions.width}px`, height: `${dimensions.height}px` }}>
                <AreaChart width={dimensions.width} height={dimensions.height} data={data.weekly_progression}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" opacity={0.3} />
                  <XAxis 
                    dataKey="week_label" 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                  />
                  <YAxis 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area 
                    type="monotone" 
                    dataKey="total_training_load" 
                    stroke="#8b5cf6" 
                    fill="#8b5cf6"
                    fillOpacity={0.3}
                    strokeWidth={2}
                  />
                </AreaChart>
              </div>
            ) : (
              <div className="text-slate-400">No training load data available</div>
            )}
          </div>
        )}
        
        {/* Pace Trends: Weekly pace progression */}
        {activeView === 'pace_trends' && (
          <div className="h-full flex items-center justify-center">
            {data.weekly_progression.filter(w => w.avg_pace).length > 0 ? (
              <div style={{ width: `${dimensions.width}px`, height: `${dimensions.height}px` }}>
                <LineChart 
                  width={dimensions.width} 
                  height={dimensions.height} 
                  data={data.weekly_progression.filter(w => w.avg_pace)}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" opacity={0.3} />
                  <XAxis 
                    dataKey="week_label" 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                  />
                  <YAxis 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                    tickFormatter={(value) => formatPace(value)}
                    domain={['dataMin - 0.5', 'dataMax + 0.5']}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line 
                    type="monotone" 
                    dataKey="avg_pace" 
                    stroke="#f59e0b" 
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                  />
                </LineChart>
              </div>
            ) : (
              <div className="text-slate-400">No pace data available</div>
            )}
          </div>
        )}
        
        {/* Heart Rate Zones: Distribution pie chart */}
        {activeView === 'zones' && (
          <div className="h-full flex items-center justify-center">
            {zoneData.filter(z => z.value > 0).length > 0 ? (
              <div style={{ width: '400px', height: '300px' }}>
                <PieChart width={400} height={300}>
                  <Pie
                    data={zoneData.filter(z => z.value > 0)}
                    cx={200}
                    cy={150}
                    innerRadius={60}
                    outerRadius={90}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {zoneData.filter(z => z.value > 0).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="bg-slate-800 border border-slate-700 rounded-lg p-3 shadow-lg">
                            <p className="text-slate-200 text-sm font-medium">{data.name}</p>
                            <p className="text-slate-400 text-xs">{data.value} activities</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </PieChart>
              </div>
            ) : (
              <div className="text-slate-400">No heart rate zone data available</div>
            )}
          </div>
        )}
      </div>
      
      {/* Time range selector */}
      <div className="flex items-center justify-center mt-4 space-x-2">
        {[
          { label: '30D', value: '30' },
          { label: '90D', value: '90' },
          { label: '6M', value: '180' }
        ].map((range) => (
          <button
            key={range.value}
            onClick={() => setTimeRange(range.value)}
            className={`px-3 py-1 text-xs font-medium rounded-md transition-all duration-200 ${
              timeRange === range.value
                ? 'bg-slate-700 text-slate-200'
                : 'text-slate-500 hover:text-slate-300 hover:bg-slate-800'
            }`}
          >
            {range.label}
          </button>
        ))}
      </div>
    </div>
  );
}