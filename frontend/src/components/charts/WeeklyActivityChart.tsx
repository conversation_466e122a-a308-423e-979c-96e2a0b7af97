'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { format, parseISO } from 'date-fns';
import { useClientOnly } from '../../hooks/useClientOnly';
import { TrendingUp, TrendingDown, Activity, Timer, MapPin, Flame } from 'lucide-react';

// Data interfaces
interface WeeklyActivityData {
  week_start: string;
  week_end: string;
  total_activities: number;
  total_distance_km: number;
  total_duration_hours: number;
  total_calories: number;
  activity_breakdown: {
    activity_type: string;
    count: number;
    distance_km: number;
    duration_hours: number;
    calories: number;
  }[];
}

interface WeeklyTrends {
  activities_change: number;
  distance_change: number;
  duration_change: number;
  calories_change: number;
}

interface WeeklyActivityChartProps {
  className?: string;
}

// Activity type colors for consistent visualization
const ACTIVITY_COLORS: Record<string, string> = {
  'running': '#ef4444',
  'cycling': '#3b82f6', 
  'walking': '#10b981',
  'swimming': '#06b6d4',
  'hiking': '#f59e0b',
  'workout': '#8b5cf6',
  'yoga': '#ec4899',
  'other': '#6b7280',
};

export default function WeeklyActivityChart({ className = '' }: WeeklyActivityChartProps) {
  const [data, setData] = useState<WeeklyActivityData | null>(null);
  const [trends, setTrends] = useState<WeeklyTrends | null>(null);
  const [weeklyHistory, setWeeklyHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'breakdown' | 'trends'>('overview');
  const [dimensions, setDimensions] = useState({ width: 800, height: 280 });
  const mounted = useClientOnly();

  // Update dimensions based on screen size
  useEffect(() => {
    if (!mounted) return;
    
    const updateDimensions = () => {
      const screenWidth = window.innerWidth;
      if (screenWidth < 768) {
        // Mobile
        setDimensions({ width: 350, height: 240 });
      } else if (screenWidth < 1024) {
        // Tablet  
        setDimensions({ width: 500, height: 260 });
      } else {
        // Desktop
        setDimensions({ width: 800, height: 280 });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [mounted]);

  useEffect(() => {
    if (!mounted) return;

    async function fetchWeeklyData() {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/activities/weekly?weeks=8');
        
        if (!response.ok) {
          throw new Error(`Failed to fetch weekly activity data: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.success && result.data) {
          // Set main current week data with defaults for safety
          const currentWeek = result.data.current_week || {};
          
          const processedData = {
            week_start: currentWeek.week_start || new Date().toISOString(),
            week_end: currentWeek.week_end || new Date().toISOString(),
            total_activities: currentWeek.total_activities || 0,
            total_distance_km: currentWeek.total_distance_km || 0,
            total_duration_hours: currentWeek.total_duration_hours || 0,
            total_calories: currentWeek.total_calories || 0,
            activity_breakdown: currentWeek.activity_breakdown || []
          };
          
          setData(processedData);

          // Set trends data separately with defaults
          const trendsData = result.data.trends || {
            activities_change: 0,
            distance_change: 0,
            duration_change: 0,
            calories_change: 0
          };
          setTrends(trendsData);
          
          // Process weekly history for trends with null safety
          const historyData = (result.data.weekly_history || [])
            .reduce((acc: any[], curr: any) => {
              const weekKey = curr.week_start;
              const existingWeek = acc.find(w => w.week_start === weekKey);
              
              if (existingWeek) {
                existingWeek.total_activities += parseInt(curr.activity_count);
                existingWeek.total_distance += parseFloat(curr.total_distance_km);
                existingWeek.total_calories += parseInt(curr.total_calories);
                existingWeek.total_duration += parseFloat(curr.total_duration_hours);
              } else {
                acc.push({
                  week_start: weekKey,
                  week_label: format(parseISO(weekKey), 'MMM dd'),
                  total_activities: parseInt(curr.activity_count),
                  total_distance: parseFloat(curr.total_distance_km),
                  total_calories: parseInt(curr.total_calories),
                  total_duration: parseFloat(curr.total_duration_hours)
                });
              }
              return acc;
            }, [])
            .sort((a: any, b: any) => new Date(a.week_start).getTime() - new Date(b.week_start).getTime())
            .slice(-8); // Last 8 weeks

          setWeeklyHistory(historyData);
        } else {
          throw new Error(result.error || 'Invalid data format');
        }
      } catch (err) {
        console.error('Error fetching weekly activity data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load weekly data');
      } finally {
        setLoading(false);
      }
    }

    fetchWeeklyData();
  }, [mounted]);

  // Don't render until mounted (client-side)
  if (!mounted) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <h3 className="text-lg font-semibold text-slate-50 mb-4">Weekly Activity</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-slate-400">Loading chart...</div>
        </div>
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-slate-800 border border-slate-700 rounded-lg p-3 shadow-lg">
          <p className="text-slate-200 text-sm font-medium mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'total_activities' && `Activities: ${entry.value}`}
              {entry.dataKey === 'total_distance' && `Distance: ${entry.value} km`}
              {entry.dataKey === 'total_calories' && `Calories: ${entry.value.toLocaleString()}`}
              {entry.dataKey === 'total_duration' && `Duration: ${entry.value.toFixed(1)}h`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const TrendIndicator = ({ value, label, icon: Icon }: { value: number, label: string, icon: any }) => {
    const isPositive = value > 0;
    const TrendIcon = isPositive ? TrendingUp : TrendingDown;
    
    return (
      <div className="flex items-center space-x-2">
        <Icon className="w-4 h-4 text-slate-400" />
        <span className="text-slate-300 text-sm">{label}</span>
        <div className={`flex items-center space-x-1 ${isPositive ? 'text-green-400' : 'text-red-400'}`}>
          <TrendIcon className="w-3 h-3" />
          <span className="text-xs font-medium">
            {isPositive ? '+' : ''}{Math.abs(value).toFixed(1)}
          </span>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-slate-50">Weekly Activity</h3>
          <div className="flex space-x-2">
            {['Overview', 'Breakdown', 'Trends'].map((view) => (
              <div key={view} className="w-16 h-6 bg-slate-700 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
        <div className="h-64 flex items-center justify-center">
          <div className="text-slate-400">Loading activity data...</div>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <h3 className="text-lg font-semibold text-slate-50 mb-4">Weekly Activity</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-red-400">Error: {error || 'No data available'}</div>
        </div>
      </div>
    );
  }

  // Prepare pie chart data for activity breakdown
  const pieData = (data?.activity_breakdown || []).map((activity) => ({
    name: activity.activity_type.charAt(0).toUpperCase() + activity.activity_type.slice(1),
    value: activity.count,
    color: ACTIVITY_COLORS[activity.activity_type.toLowerCase()] || ACTIVITY_COLORS.other
  }));

  return (
    <div className={`chart-container p-6 h-80 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-slate-50">Weekly Activity</h3>
          <p className="text-sm text-slate-400">
            {data?.week_start && data?.week_end ? 
              `${format(parseISO(data.week_start), 'MMM dd')} - ${format(parseISO(data.week_end), 'MMM dd')}` : 
              'Current Week'
            }
          </p>
        </div>
        <div className="flex space-x-2">
          {[
            { key: 'overview', label: 'Overview' },
            { key: 'breakdown', label: 'Types' },
            { key: 'trends', label: 'Trends' }
          ].map((view) => (
            <button
              key={view.key}
              onClick={() => setActiveView(view.key as any)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                activeView === view.key
                  ? 'bg-violet-500 text-white'
                  : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800'
              }`}
            >
              {view.label}
            </button>
          ))}
        </div>
      </div>
      
      <div className="h-64">
        {/* Overview: Current week stats with trends */}
        {activeView === 'overview' && (
          <div className="h-full">
            <div className="grid grid-cols-2 gap-4 h-full">
              {/* Current Week Stats */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-slate-300 mb-3">This Week</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-blue-400" />
                      <span className="text-slate-300 text-sm">Activities</span>
                    </div>
                    <span className="text-slate-50 font-semibold">{data?.total_activities || 0}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-green-400" />
                      <span className="text-slate-300 text-sm">Distance</span>
                    </div>
                    <span className="text-slate-50 font-semibold">{(data?.total_distance_km || 0).toFixed(1)} km</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Timer className="w-4 h-4 text-yellow-400" />
                      <span className="text-slate-300 text-sm">Duration</span>
                    </div>
                    <span className="text-slate-50 font-semibold">{(data?.total_duration_hours || 0).toFixed(1)}h</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Flame className="w-4 h-4 text-red-400" />
                      <span className="text-slate-300 text-sm">Calories</span>
                    </div>
                    <span className="text-slate-50 font-semibold">{(data?.total_calories || 0).toLocaleString()}</span>
                  </div>
                </div>
              </div>
              
              {/* Week-over-Week Trends */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-slate-300 mb-3">Week-over-Week</h4>
                <div className="space-y-3">
                  <TrendIndicator 
                    value={trends?.activities_change || 0} 
                    label="Activities" 
                    icon={Activity}
                  />
                  <TrendIndicator 
                    value={trends?.distance_change || 0} 
                    label="Distance (km)" 
                    icon={MapPin}
                  />
                  <TrendIndicator 
                    value={trends?.duration_change || 0} 
                    label="Duration (h)" 
                    icon={Timer}
                  />
                  <TrendIndicator 
                    value={trends?.calories_change || 0} 
                    label="Calories" 
                    icon={Flame}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Breakdown: Activity types pie chart */}
        {activeView === 'breakdown' && (
          <div className="h-full flex items-center justify-center">
            {pieData.length > 0 ? (
              <div style={{ width: '400px', height: '300px' }}>
                <PieChart width={400} height={300}>
                  <Pie
                    data={pieData}
                    cx={200}
                    cy={150}
                    innerRadius={60}
                    outerRadius={90}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="bg-slate-800 border border-slate-700 rounded-lg p-3 shadow-lg">
                            <p className="text-slate-200 text-sm font-medium">{data.name}</p>
                            <p className="text-slate-400 text-xs">{data.value} activities</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </PieChart>
              </div>
            ) : (
              <div className="text-slate-400">No activity data available for pie chart</div>
            )}
          </div>
        )}
        
        {/* Trends: Weekly history bar chart */}
        {activeView === 'trends' && (
          <div className="h-full flex items-center justify-center">
            {weeklyHistory.length > 0 ? (
              <div style={{ width: '600px', height: '300px' }}>
                <BarChart width={600} height={300} data={weeklyHistory} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" opacity={0.3} />
                  <XAxis 
                    dataKey="week_label" 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                  />
                  <YAxis 
                    stroke="#94a3b8" 
                    fontSize={12} 
                    tickLine={false} 
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar 
                    dataKey="total_activities" 
                    fill="#3b82f6" 
                    radius={[2, 2, 0, 0]}
                  />
                </BarChart>
              </div>
            ) : (
              <div className="text-slate-400">No weekly history data for trends chart</div>
            )}
          </div>
        )}
        
        {/* Empty state for breakdown */}
        {activeView === 'breakdown' && pieData.length === 0 && (
          <div className="h-full flex items-center justify-center">
            <div className="text-slate-400">No activities this week</div>
          </div>
        )}
      </div>
    </div>
  );
}