'use client';

import { useState, useEffect } from 'react';
import { Line<PERSON><PERSON>, Line, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ReferenceLine } from 'recharts';
import { format } from 'date-fns';
import { useClientOnly } from '../../hooks/useClientOnly';

interface StepsData {
  date: string;
  steps: number;
  formattedDate: string;
}

interface StepsLineChartProps {
  className?: string;
}

export default function StepsLineChart({ className = '' }: StepsLineChartProps) {
  const [data, setData] = useState<StepsData[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeRange, setActiveRange] = useState('1m');
  const [error, setError] = useState<string | null>(null);
  const [dimensions, setDimensions] = useState({ width: 800, height: 250 });
  const mounted = useClientOnly();

  // Update dimensions based on screen size
  useEffect(() => {
    if (!mounted) return;
    
    const updateDimensions = () => {
      const screenWidth = window.innerWidth;
      if (screenWidth < 768) {
        // Mobile
        setDimensions({ width: 350, height: 200 });
      } else if (screenWidth < 1024) {
        // Tablet
        setDimensions({ width: 500, height: 220 });
      } else {
        // Desktop
        setDimensions({ width: 800, height: 250 });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [mounted]);

  const STEPS_GOAL = 10000;

  // Helper function to get time range label
  const getTimeRangeLabel = (range: string) => {
    const labels: Record<string, string> = {
      '7d': 'Last 7 days performance',
      '14d': 'Last 14 days performance',
      '1m': 'Last 30 days performance',
      '3m': 'Last 3 months performance',
      '6m': 'Last 6 months performance',
      '1y': 'Last year performance',
      'all': 'All time performance',
    };
    return labels[range] || 'Performance overview';
  };

  const timeRanges = [
    { label: '7D', value: '7d', days: 7 },
    { label: '14D', value: '14d', days: 14 },
    { label: '1M', value: '1m', days: 30 },
    { label: '3M', value: '3m', days: 90 },
    { label: '6M', value: '6m', days: 180 },
    { label: '1Y', value: '1y', days: 365 },
    { label: 'All', value: 'all', days: 1095 }, // 3 years max
  ];

  useEffect(() => {
    if (!mounted) return;

    async function fetchStepsData() {
      try {
        setLoading(true);
        setError(null);

        // Convert range to days
        const daysMap: Record<string, number> = {
          '7d': 7,
          '14d': 14,
          '1m': 30,
          '3m': 90,
          '6m': 180,
          '1y': 365,
          'all': 1095, // 3 years max
        };

        const days = daysMap[activeRange] || 30;
        const response = await fetch(`/api/daily-summaries?days=${days}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch steps data');
        }

        const result = await response.json();
        
        if (result.success && result.data) {
          const formattedData: StepsData[] = result.data.map((item: any) => ({
            date: item.summary_date,
            steps: item.total_steps || 0,
            formattedDate: format(new Date(item.summary_date), 'MMM dd'),
          })).sort((a: StepsData, b: StepsData) => new Date(a.date).getTime() - new Date(b.date).getTime());

          setData(formattedData);
        } else {
          throw new Error(result.error || 'Invalid data format');
        }
      } catch (err) {
        console.error('Error fetching steps data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load chart data');
      } finally {
        setLoading(false);
      }
    }

    fetchStepsData();
  }, [activeRange, mounted]);

  // Don't render until mounted (client-side)
  if (!mounted) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <h3 className="text-lg font-semibold text-slate-50 mb-4">Daily Steps Trend</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-slate-400">Loading chart...</div>
        </div>
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const steps = payload[0].value;
      return (
        <div className="bg-slate-800 border border-slate-700 rounded-lg p-3 shadow-lg">
          <p className="text-slate-200 text-sm">{label}</p>
          <p className="text-blue-400 font-semibold">
            {steps.toLocaleString()} steps
          </p>
          <p className="text-xs text-slate-400">
            Goal: {STEPS_GOAL.toLocaleString()} steps
          </p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-slate-50">Daily Steps Trend</h3>
          <div className="flex space-x-2">
            {timeRanges.map((range) => (
              <div key={range.value} className="w-8 h-6 bg-slate-700 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
        <div className="h-64 flex items-center justify-center">
          <div className="text-slate-400">Loading chart data...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`chart-container p-6 h-80 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-slate-50">Daily Steps Trend</h3>
          <div className="flex space-x-2">
            {timeRanges.map((range) => (
              <button
                key={range.value}
                onClick={() => setActiveRange(range.value)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                  activeRange === range.value
                    ? 'bg-violet-500 text-white'
                    : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800'
                }`}
              >
                {range.label}
              </button>
            ))}
          </div>
        </div>
        <div className="h-64 flex items-center justify-center">
          <div className="text-red-400">Error: {error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`chart-container p-6 h-80 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-slate-50">Daily Steps Trend</h3>
          <p className="text-sm text-slate-400">
            {data.length > 0 ? (
              `Last ${data.length} days performance`
            ) : (
              getTimeRangeLabel(activeRange)
            )}
          </p>
        </div>
        <div className="flex space-x-2">
          {timeRanges.map((range) => (
            <button
              key={range.value}
              onClick={() => setActiveRange(range.value)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                activeRange === range.value
                  ? 'bg-violet-500 text-white'
                  : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800'
              }`}
            >
              {range.label}
            </button>
          ))}
        </div>
      </div>
      
      <div className="h-64 flex items-center justify-center">
        {data.length > 0 ? (
          <LineChart 
            width={dimensions.width} 
            height={dimensions.height} 
            data={data} 
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="#334155" 
              opacity={0.3}
            />
            <XAxis 
              dataKey="formattedDate"
              stroke="#94a3b8"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis 
              stroke="#94a3b8"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
            />
            <Tooltip content={<CustomTooltip />} />
            
            {/* Goal reference line */}
            <ReferenceLine 
              y={STEPS_GOAL} 
              stroke="#ef4444" 
              strokeDasharray="5 5" 
              strokeOpacity={0.6}
            />
            
            <Line 
              type="monotone" 
              dataKey="steps" 
              stroke="#3b82f6" 
              strokeWidth={2}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}
              activeDot={{ r: 5, stroke: '#3b82f6', strokeWidth: 2, fill: '#1e293b' }}
              connectNulls={false}
              animationDuration={1000}
            />
          </LineChart>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-slate-400">No data available for chart</div>
          </div>
        )}
      </div>
      
      {/* Chart legend */}
      <div className="flex items-center justify-center mt-4 space-x-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-0.5 bg-blue-500"></div>
          <span className="text-slate-400">Daily Steps</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-0.5 bg-red-500 border-dashed border-t border-red-500"></div>
          <span className="text-slate-400">Goal ({STEPS_GOAL.toLocaleString()})</span>
        </div>
      </div>
    </div>
  );
}