import { NextResponse } from 'next/server';
import { query } from '../../../../lib/db';

// Response interfaces for TypeScript safety
interface WeeklyActivitySummary {
  week_start: string;
  week_end: string;
  activity_type: string;
  activity_count: number;
  total_distance_km: number;
  total_duration_hours: number;
  total_calories: number;
  avg_heart_rate: number | null;
}

interface WeeklyStats {
  current_week: {
    week_start: string;
    week_end: string;
    total_activities: number;
    total_distance_km: number;
    total_duration_hours: number;
    total_calories: number;
    activity_breakdown: {
      activity_type: string;
      count: number;
      distance_km: number;
      duration_hours: number;
      calories: number;
    }[];
  };
  previous_week: {
    total_activities: number;
    total_distance_km: number;
    total_duration_hours: number;
    total_calories: number;
  };
  trends: {
    activities_change: number;
    distance_change: number;
    duration_change: number;
    calories_change: number;
  };
  weekly_history: WeeklyActivitySummary[];
}

// GET /api/activities/weekly
// Returns comprehensive weekly activity analytics and trends
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const weeks = parseInt(searchParams.get('weeks') || '12'); // Default 12 weeks history
    
    console.log(`Fetching weekly activity data for ${weeks} weeks...`);

    // 1. Current week activities with detailed breakdown
    const currentWeekResult = await query(`
      WITH current_week AS (
        SELECT 
          DATE_TRUNC('week', NOW()) as week_start,
          DATE_TRUNC('week', NOW()) + INTERVAL '6 days' as week_end
      )
      SELECT 
        cw.week_start,
        cw.week_end,
        COALESCE(a.activity_type, 'unknown') as activity_type,
        COUNT(a.activity_id) as activity_count,
        COALESCE(ROUND(SUM(a.distance_km)::numeric, 2), 0) as total_distance_km,
        COALESCE(ROUND(SUM(a.duration_s::numeric / 3600), 2), 0) as total_duration_hours,
        COALESCE(SUM(a.calories), 0) as total_calories,
        COALESCE(ROUND(AVG(a.average_hr)), 0) as avg_heart_rate
      FROM current_week cw
      LEFT JOIN activities a ON a.start_time_gmt >= cw.week_start 
                            AND a.start_time_gmt < cw.week_start + INTERVAL '7 days'
      GROUP BY cw.week_start, cw.week_end, a.activity_type
      ORDER BY activity_count DESC
    `);

    // 2. Previous week totals for comparison
    const previousWeekResult = await query(`
      SELECT 
        COUNT(activity_id) as total_activities,
        COALESCE(ROUND(SUM(distance_km)::numeric, 2), 0) as total_distance_km,
        COALESCE(ROUND(SUM(duration_s::numeric / 3600), 2), 0) as total_duration_hours,
        COALESCE(SUM(calories), 0) as total_calories
      FROM activities 
      WHERE start_time_gmt >= DATE_TRUNC('week', NOW()) - INTERVAL '7 days'
        AND start_time_gmt < DATE_TRUNC('week', NOW())
    `);

    // 3. Weekly history for trend analysis
    const weeklyHistoryResult = await query(`
      WITH weekly_periods AS (
        SELECT 
          DATE_TRUNC('week', NOW()) - INTERVAL '${weeks - 1} weeks' + (INTERVAL '1 week' * generate_series(0, ${weeks - 1})) as week_start
      )
      SELECT 
        wp.week_start,
        wp.week_start + INTERVAL '6 days' as week_end,
        COALESCE(a.activity_type, 'No Activity') as activity_type,
        COUNT(a.activity_id) as activity_count,
        COALESCE(ROUND(SUM(a.distance_km)::numeric, 2), 0) as total_distance_km,
        COALESCE(ROUND(SUM(a.duration_s::numeric / 3600), 2), 0) as total_duration_hours,
        COALESCE(SUM(a.calories), 0) as total_calories,
        COALESCE(ROUND(AVG(a.average_hr)), 0) as avg_heart_rate
      FROM weekly_periods wp
      LEFT JOIN activities a ON a.start_time_gmt >= wp.week_start 
                            AND a.start_time_gmt < wp.week_start + INTERVAL '7 days'
      GROUP BY wp.week_start, wp.week_start + INTERVAL '6 days', a.activity_type
      ORDER BY wp.week_start DESC, activity_count DESC
    `);

    // Process current week data
    const currentWeekData = currentWeekResult.rows;
    const currentWeekTotals = currentWeekData.reduce((acc, row) => {
      if (row.activity_type !== 'unknown') {
        acc.total_activities += parseInt(row.activity_count);
        acc.total_distance_km += parseFloat(row.total_distance_km);
        acc.total_duration_hours += parseFloat(row.total_duration_hours);
        acc.total_calories += parseInt(row.total_calories);
      }
      return acc;
    }, {
      total_activities: 0,
      total_distance_km: 0,
      total_duration_hours: 0,
      total_calories: 0
    });

    // Process previous week data
    const previousWeekData = previousWeekResult.rows[0] || {
      total_activities: 0,
      total_distance_km: 0,
      total_duration_hours: 0,
      total_calories: 0
    };

    // Calculate trends (week-over-week changes)
    const trends = {
      activities_change: currentWeekTotals.total_activities - parseInt(previousWeekData.total_activities),
      distance_change: Math.round((currentWeekTotals.total_distance_km - parseFloat(previousWeekData.total_distance_km)) * 100) / 100,
      duration_change: Math.round((currentWeekTotals.total_duration_hours - parseFloat(previousWeekData.total_duration_hours)) * 100) / 100,
      calories_change: currentWeekTotals.total_calories - parseInt(previousWeekData.total_calories)
    };

    // Format activity breakdown for current week
    const activityBreakdown = currentWeekData
      .filter(row => row.activity_type !== 'unknown' && parseInt(row.activity_count) > 0)
      .map(row => ({
        activity_type: row.activity_type,
        count: parseInt(row.activity_count),
        distance_km: parseFloat(row.total_distance_km),
        duration_hours: parseFloat(row.total_duration_hours),
        calories: parseInt(row.total_calories)
      }));

    const stats: WeeklyStats = {
      current_week: {
        week_start: currentWeekData[0]?.week_start || new Date().toISOString(),
        week_end: currentWeekData[0]?.week_end || new Date().toISOString(),
        ...currentWeekTotals,
        activity_breakdown: activityBreakdown
      },
      previous_week: {
        total_activities: parseInt(previousWeekData.total_activities),
        total_distance_km: parseFloat(previousWeekData.total_distance_km),
        total_duration_hours: parseFloat(previousWeekData.total_duration_hours),
        total_calories: parseInt(previousWeekData.total_calories)
      },
      trends,
      weekly_history: weeklyHistoryResult.rows as WeeklyActivitySummary[]
    };

    console.log(`Weekly stats calculated for ${weeks} weeks`);

    return NextResponse.json({
      success: true,
      data: stats,
      meta: {
        timestamp: new Date().toISOString(),
        weeks_analyzed: weeks,
        current_week_start: stats.current_week.week_start,
        current_week_end: stats.current_week.week_end
      },
    });

  } catch (error) {
    console.error('Error fetching weekly activity data:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch weekly activity statistics',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}