import { NextResponse } from 'next/server';
import { query } from '../../../../lib/db';

interface SyncStatus {
  service_status: 'healthy' | 'warning' | 'error';
  last_activity_sync: string | null;
  last_summary_sync: string | null;
  total_activities: number;
  total_summaries: number;
  recent_sync_stats: {
    activities_last_24h: number;
    summaries_last_7days: number;
  };
}

// GET /api/sync/status
// Returns data sync service health and statistics
export async function GET() {
  try {
    console.log('Checking sync service status...');

    // Get latest activity sync time
    const lastActivityResult = await query(`
      SELECT MAX(processed_at) as last_sync
      FROM activities
    `);
    const lastActivitySync = lastActivityResult.rows[0]?.last_sync;

    // Get latest summary sync time
    const lastSummaryResult = await query(`
      SELECT MAX(processed_at) as last_sync
      FROM daily_summaries
    `);
    const lastSummarySync = lastSummaryResult.rows[0]?.last_sync;

    // Get total counts
    const totalActivitiesResult = await query('SELECT COUNT(*) as total FROM activities');
    const totalActivities = parseInt(totalActivitiesResult.rows[0].total);

    const totalSummariesResult = await query('SELECT COUNT(*) as total FROM daily_summaries');
    const totalSummaries = parseInt(totalSummariesResult.rows[0].total);

    // Get recent sync statistics
    const recentActivitiesResult = await query(`
      SELECT COUNT(*) as recent_count
      FROM activities
      WHERE processed_at >= NOW() - INTERVAL '24 hours'
    `);
    const activitiesLast24h = parseInt(recentActivitiesResult.rows[0].recent_count);

    const recentSummariesResult = await query(`
      SELECT COUNT(*) as recent_count
      FROM daily_summaries
      WHERE processed_at >= NOW() - INTERVAL '7 days'
    `);
    const summariesLast7days = parseInt(recentSummariesResult.rows[0].recent_count);

    // Determine service status
    let serviceStatus: 'healthy' | 'warning' | 'error' = 'healthy';
    const now = new Date();
    const lastSyncTime = new Date(Math.max(
      new Date(lastActivitySync || 0).getTime(),
      new Date(lastSummarySync || 0).getTime()
    ));
    const hoursSinceLastSync = (now.getTime() - lastSyncTime.getTime()) / (1000 * 60 * 60);

    if (hoursSinceLastSync > 48) {
      serviceStatus = 'error';
    } else if (hoursSinceLastSync > 25) {
      serviceStatus = 'warning';
    }

    const status: SyncStatus = {
      service_status: serviceStatus,
      last_activity_sync: lastActivitySync,
      last_summary_sync: lastSummarySync,
      total_activities: totalActivities,
      total_summaries: totalSummaries,
      recent_sync_stats: {
        activities_last_24h: activitiesLast24h,
        summaries_last_7days: summariesLast7days,
      },
    };

    console.log('Sync status calculated:', status);

    return NextResponse.json({
      success: true,
      data: status,
      meta: {
        timestamp: new Date().toISOString(),
        hours_since_last_sync: Math.round(hoursSinceLastSync * 10) / 10,
      },
    });

  } catch (error) {
    console.error('Error checking sync status:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check sync status',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
      },
      { status: 500 }
    );
  }
}