import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET() {
  try {
    // Get sync analytics and data quality metrics
    const [
      syncStats,
      dataQuality,
      recentErrors
    ] = await Promise.all([
      // Sync statistics
      query(`
        SELECT 
          'daily_summaries' as data_type,
          COUNT(*) as total_records,
          MAX(summary_date) as latest_date,
          COUNT(*) FILTER (WHERE processed_at > NOW() - INTERVAL '24 hours') as synced_last_24h,
          COUNT(*) FILTER (WHERE processed_at > NOW() - INTERVAL '7 days') as synced_last_7d
        FROM daily_summaries
        UNION ALL
        SELECT 
          'activities' as data_type,
          COUNT(*) as total_records,
          MAX(start_time::date) as latest_date,
          COUNT(*) FILTER (WHERE processed_at > NOW() - INTERVAL '24 hours') as synced_last_24h,
          COUNT(*) FILTER (WHERE processed_at > NOW() - INTERVAL '7 days') as synced_last_7d
        FROM activities
      `),
      
      // Data quality metrics
      query(`
        SELECT 
          'daily_summaries' as table_name,
          COUNT(*) FILTER (WHERE total_steps IS NULL) as null_steps,
          COUNT(*) FILTER (WHERE calories_total IS NULL) as null_calories,
          COUNT(*) FILTER (WHERE total_steps > 100000) as extreme_steps,
          COUNT(*) FILTER (WHERE total_steps < 0) as negative_steps
        FROM daily_summaries
        WHERE summary_date > CURRENT_DATE - INTERVAL '30 days'
      `),
      
      // Recent processing errors (simulated - would come from logs in real implementation)
      query(`
        SELECT 
          summary_date,
          CASE 
            WHEN total_steps IS NULL AND calories_total IS NULL THEN 'missing_data'
            WHEN total_steps > 50000 THEN 'suspicious_steps'
            ELSE 'unknown'
          END as error_type
        FROM daily_summaries
        WHERE (total_steps IS NULL AND calories_total IS NULL) 
           OR total_steps > 50000
        ORDER BY summary_date DESC
        LIMIT 10
      `)
    ]);

    // Calculate data completeness score
    const dailySummaryStats = syncStats.find(s => s.data_type === 'daily_summaries');
    const expectedDays = 30; // Last 30 days
    const actualDays = dailySummaryStats ? dailySummaryStats.total_records : 0;
    const completenessScore = Math.min(100, (actualDays / expectedDays) * 100);

    // Calculate data quality score
    const qualityMetrics = dataQuality[0];
    const totalRecords = dailySummaryStats ? dailySummaryStats.total_records : 1;
    const qualityIssues = (qualityMetrics?.null_steps || 0) + 
                         (qualityMetrics?.null_calories || 0) + 
                         (qualityMetrics?.extreme_steps || 0) + 
                         (qualityMetrics?.negative_steps || 0);
    const qualityScore = Math.max(0, 100 - (qualityIssues / totalRecords) * 100);

    return NextResponse.json({
      success: true,
      data: {
        sync_statistics: syncStats,
        data_quality: {
          completeness_score: Math.round(completenessScore),
          quality_score: Math.round(qualityScore),
          metrics: qualityMetrics,
          recent_errors: recentErrors
        },
        health_indicators: {
          sync_freshness: dailySummaryStats?.synced_last_24h > 0 ? 'healthy' : 'stale',
          data_completeness: completenessScore > 80 ? 'good' : completenessScore > 50 ? 'fair' : 'poor',
          data_quality: qualityScore > 90 ? 'excellent' : qualityScore > 70 ? 'good' : 'needs_attention'
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        analysis_period: '30 days'
      }
    });

  } catch (error) {
    console.error('Sync analytics error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch sync analytics',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}