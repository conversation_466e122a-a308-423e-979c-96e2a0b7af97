import { NextResponse } from 'next/server';
import { query } from '../../../../lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '90');
    const activityType = searchParams.get('activity_type') || 'all';

    console.log(`Fetching performance analytics for ${days} days, activity: ${activityType}`);

    // Base query for activities with performance metrics
    const baseWhere = `
      WHERE start_time_gmt >= NOW() - INTERVAL '${days} days'
        AND distance_km > 0 
        AND duration_s > 0
        ${activityType !== 'all' ? `AND activity_type = '${activityType}'` : ''}
    `;

    // Get activities with calculated performance metrics
    const activitiesQuery = `
      SELECT 
        activity_id,
        activity_type,
        start_time_gmt::date as activity_date,
        distance_km,
        duration_s,
        average_hr,
        max_hr,
        calories,
        -- Calculate pace (minutes per km)
        CASE 
          WHEN distance_km > 0 THEN (duration_s / 60.0) / distance_km
          ELSE NULL
        END as pace_per_km,
        -- Calculate speed (km/h)
        CASE 
          WHEN duration_s > 0 THEN (distance_km * 3600.0) / duration_s
          ELSE NULL
        END as speed_kmh,
        -- Calculate training load (duration * intensity factor)
        CASE 
          WHEN average_hr IS NOT NULL AND average_hr > 0 THEN
            (duration_s / 60.0) * (average_hr / 180.0)
          ELSE duration_s / 60.0
        END as training_load
      FROM activities 
      ${baseWhere}
      ORDER BY start_time_gmt DESC
    `;

    const activitiesResult = await query(activitiesQuery);
    const activities = activitiesResult.rows;

    if (activities.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          activities: [],
          performance_summary: {},
          training_zones: {},
          progression_metrics: {}
        },
        meta: {
          timestamp: new Date().toISOString(),
          period_days: days,
          total_activities: 0
        }
      });
    }

    // Calculate performance summary
    const totalActivities = activities.length;
    const totalDistance = activities.reduce((sum, a) => sum + parseFloat(a.distance_km || 0), 0);
    const totalDuration = activities.reduce((sum, a) => sum + parseFloat(a.duration_s || 0), 0);
    const totalCalories = activities.reduce((sum, a) => sum + (a.calories || 0), 0);
    const totalTrainingLoad = activities.reduce((sum, a) => sum + parseFloat(a.training_load || 0), 0);

    // Calculate average metrics
    const avgDistance = totalDistance / totalActivities;
    const avgDuration = totalDuration / totalActivities;
    const avgPace = activities
      .filter(a => a.pace_per_km)
      .reduce((sum, a) => sum + parseFloat(a.pace_per_km), 0) / 
      activities.filter(a => a.pace_per_km).length;
    
    const avgHeartRate = activities
      .filter(a => a.average_hr)
      .reduce((sum, a) => sum + a.average_hr, 0) / 
      activities.filter(a => a.average_hr).length;

    // Group by activity type for analysis
    const activityTypeStats = activities.reduce((acc, activity) => {
      const type = activity.activity_type;
      if (!acc[type]) {
        acc[type] = {
          count: 0,
          total_distance: 0,
          total_duration: 0,
          total_calories: 0,
          paces: [],
          heart_rates: []
        };
      }
      
      acc[type].count++;
      acc[type].total_distance += parseFloat(activity.distance_km || 0);
      acc[type].total_duration += parseFloat(activity.duration_s || 0);
      acc[type].total_calories += activity.calories || 0;
      
      if (activity.pace_per_km) acc[type].paces.push(parseFloat(activity.pace_per_km));
      if (activity.average_hr) acc[type].heart_rates.push(activity.average_hr);
      
      return acc;
    }, {} as Record<string, any>);

    // Calculate training zones (simplified heart rate zones)
    const maxHR = Math.max(...activities.filter(a => a.max_hr).map(a => a.max_hr));
    const trainingZones = {
      zone1_recovery: { min: 0, max: Math.round(maxHR * 0.6), name: 'Recovery' },
      zone2_aerobic: { min: Math.round(maxHR * 0.6), max: Math.round(maxHR * 0.7), name: 'Aerobic Base' },
      zone3_tempo: { min: Math.round(maxHR * 0.7), max: Math.round(maxHR * 0.8), name: 'Tempo' },
      zone4_threshold: { min: Math.round(maxHR * 0.8), max: Math.round(maxHR * 0.9), name: 'Threshold' },
      zone5_vo2max: { min: Math.round(maxHR * 0.9), max: maxHR, name: 'VO2 Max' }
    };

    // Calculate zone distribution
    const zoneDistribution = activities
      .filter(a => a.average_hr)
      .reduce((acc, activity) => {
        const hr = activity.average_hr;
        if (hr <= trainingZones.zone1_recovery.max) acc.zone1++;
        else if (hr <= trainingZones.zone2_aerobic.max) acc.zone2++;
        else if (hr <= trainingZones.zone3_tempo.max) acc.zone3++;
        else if (hr <= trainingZones.zone4_threshold.max) acc.zone4++;
        else acc.zone5++;
        return acc;
      }, { zone1: 0, zone2: 0, zone3: 0, zone4: 0, zone5: 0 });

    // Weekly progression analysis
    const weeklyData: any[] = [];
    const weeksMap = new Map();

    activities.forEach(activity => {
      const date = new Date(activity.activity_date);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay()); // Get Sunday of the week
      const weekKey = weekStart.toISOString().split('T')[0];

      if (!weeksMap.has(weekKey)) {
        weeksMap.set(weekKey, {
          week_start: weekKey,
          activities: [],
          total_distance: 0,
          total_duration: 0,
          total_training_load: 0,
          avg_pace: 0,
          avg_hr: 0
        });
      }

      const weekData = weeksMap.get(weekKey);
      weekData.activities.push(activity);
      weekData.total_distance += parseFloat(activity.distance_km);
      weekData.total_duration += parseFloat(activity.duration_s);
      weekData.total_training_load += parseFloat(activity.training_load || 0);
    });

    // Calculate weekly averages
    Array.from(weeksMap.values()).forEach(week => {
      const activities = week.activities;
      week.activity_count = activities.length;
      week.avg_distance = week.total_distance / activities.length;
      week.avg_duration = week.total_duration / activities.length;
      
      const validPaces = activities.filter((a: any) => a.pace_per_km).map((a: any) => parseFloat(a.pace_per_km));
      const validHRs = activities.filter((a: any) => a.average_hr).map((a: any) => a.average_hr);
      
      week.avg_pace = validPaces.length > 0 ? validPaces.reduce((a, b) => a + b, 0) / validPaces.length : null;
      week.avg_hr = validHRs.length > 0 ? validHRs.reduce((a, b) => a + b, 0) / validHRs.length : null;
      week.week_label = new Date(week.week_start).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      
      weeklyData.push(week);
    });

    weeklyData.sort((a, b) => new Date(a.week_start).getTime() - new Date(b.week_start).getTime());

    // Personal records (best performances)
    const personalRecords = {
      fastest_5k: null,
      longest_distance: Math.max(...activities.map(a => parseFloat(a.distance_km))),
      longest_duration: Math.max(...activities.map(a => parseFloat(a.duration_s))),
      highest_training_load: Math.max(...activities.map(a => parseFloat(a.training_load || 0)))
    };

    // Find fastest 5K equivalent pace
    const running5kEquivalent = activities
      .filter(a => a.activity_type === 'running' && parseFloat(a.distance_km) >= 5)
      .map(a => ({ ...a, pace: parseFloat(a.pace_per_km) }))
      .sort((a, b) => a.pace - b.pace);
    
    if (running5kEquivalent.length > 0) {
      personalRecords.fastest_5k = running5kEquivalent[0].pace;
    }

    return NextResponse.json({
      success: true,
      data: {
        activities: activities.slice(0, 50), // Limit recent activities
        performance_summary: {
          total_activities: totalActivities,
          total_distance: Math.round(totalDistance * 10) / 10,
          total_duration_hours: Math.round(totalDuration / 3600 * 10) / 10,
          total_calories: totalCalories,
          total_training_load: Math.round(totalTrainingLoad),
          avg_distance: Math.round(avgDistance * 10) / 10,
          avg_duration_minutes: Math.round(avgDuration / 60),
          avg_pace: avgPace ? Math.round(avgPace * 10) / 10 : null,
          avg_heart_rate: avgHeartRate ? Math.round(avgHeartRate) : null
        },
        activity_type_breakdown: Object.keys(activityTypeStats).map(type => ({
          activity_type: type,
          count: activityTypeStats[type].count,
          total_distance: Math.round(activityTypeStats[type].total_distance * 10) / 10,
          avg_pace: activityTypeStats[type].paces.length > 0 ? 
            Math.round((activityTypeStats[type].paces.reduce((a: number, b: number) => a + b, 0) / activityTypeStats[type].paces.length) * 10) / 10 : null,
          avg_heart_rate: activityTypeStats[type].heart_rates.length > 0 ?
            Math.round(activityTypeStats[type].heart_rates.reduce((a: number, b: number) => a + b, 0) / activityTypeStats[type].heart_rates.length) : null
        })),
        training_zones: {
          zones: trainingZones,
          distribution: zoneDistribution,
          max_hr: maxHR
        },
        weekly_progression: weeklyData,
        personal_records: personalRecords
      },
      meta: {
        timestamp: new Date().toISOString(),
        period_days: days,
        activity_filter: activityType,
        total_records: totalActivities
      }
    });

  } catch (error) {
    console.error('Error fetching performance analytics:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch performance analytics',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
      },
      { status: 500 }
    );
  }
}