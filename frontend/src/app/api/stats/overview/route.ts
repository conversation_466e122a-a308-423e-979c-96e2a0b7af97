import { NextResponse } from 'next/server';
import { query } from '../../../../lib/db';

// Response type for TypeScript safety
interface OverviewStats {
  totalActivities: number;
  avgDailySteps: number;
  currentWeekCalories: number;
  latestStressLevel: number | null;
  avgHeartRate: number | null;
  latestBodyBattery: number | null;
  avgSleepHours: number | null;
  trends: {
    stepsChange: number;
    caloriesChange: number;
    heartRateChange: number;
    stressChange: number;
  };
}

// GET /api/stats/overview
// Returns key fitness metrics for dashboard cards
export async function GET() {
  try {
    console.log('Fetching overview stats...');

    // 1. Total Activities Count
    const activitiesResult = await query(`
      SELECT COUNT(*) as total 
      FROM activities
    `);
    const totalActivities = parseInt(activitiesResult.rows[0].total);

    // 2. Average Daily Steps (Last 30 Days)
    const stepsResult = await query(`
      SELECT 
        ROUND(AVG(total_steps)) as avg_steps,
        COUNT(*) as days_counted
      FROM daily_summaries 
      WHERE summary_date >= NOW() - INTERVAL '30 days'
        AND total_steps IS NOT NULL
    `);
    const avgDailySteps = parseInt(stepsResult.rows[0].avg_steps) || 0;

    // 3. Current Week Calories Total
    const weekCaloriesResult = await query(`
      SELECT COALESCE(SUM(calories_total), 0) as week_calories
      FROM daily_summaries 
      WHERE summary_date >= DATE_TRUNC('week', NOW())
        AND calories_total IS NOT NULL
    `);
    const currentWeekCalories = parseInt(weekCaloriesResult.rows[0].week_calories);

    // 4. Latest Stress Level
    const stressResult = await query(`
      SELECT avg_stress_level
      FROM daily_summaries 
      WHERE avg_stress_level IS NOT NULL AND avg_stress_level >= 0
      ORDER BY summary_date DESC 
      LIMIT 1
    `);
    const latestStressLevel = stressResult.rows[0]?.avg_stress_level || null;

    // 5. Average Heart Rate (Last 7 Days)
    const heartRateResult = await query(`
      SELECT ROUND(AVG((min_hr + max_hr) / 2)) as avg_hr
      FROM daily_summaries 
      WHERE summary_date >= NOW() - INTERVAL '7 days'
        AND min_hr IS NOT NULL AND max_hr IS NOT NULL
    `);
    const avgHeartRate = heartRateResult.rows[0]?.avg_hr || null;

    // 6. Latest Body Battery
    const bodyBatteryResult = await query(`
      SELECT body_battery_max
      FROM daily_summaries 
      WHERE body_battery_max IS NOT NULL
      ORDER BY summary_date DESC 
      LIMIT 1
    `);
    const latestBodyBattery = bodyBatteryResult.rows[0]?.body_battery_max || null;

    // 7. Average Sleep Hours (Last 7 Days)
    const sleepResult = await query(`
      SELECT ROUND(AVG(sleep_duration_s) / 3600, 1) as avg_sleep_hours
      FROM daily_summaries 
      WHERE summary_date >= NOW() - INTERVAL '7 days'
        AND sleep_duration_s IS NOT NULL AND sleep_duration_s > 0
    `);
    const avgSleepHours = sleepResult.rows[0]?.avg_sleep_hours || null;

    // 8. Trend Calculations (Compare with previous period)
    const previousStepsResult = await query(`
      SELECT ROUND(AVG(total_steps)) as avg_steps
      FROM daily_summaries 
      WHERE summary_date >= NOW() - INTERVAL '60 days'
        AND summary_date < NOW() - INTERVAL '30 days'
        AND total_steps IS NOT NULL
    `);
    const previousAvgSteps = parseInt(previousStepsResult.rows[0].avg_steps) || 0;
    const stepsChange = avgDailySteps - previousAvgSteps;

    const previousWeekCaloriesResult = await query(`
      SELECT COALESCE(SUM(calories_total), 0) as prev_week_calories
      FROM daily_summaries 
      WHERE summary_date >= DATE_TRUNC('week', NOW()) - INTERVAL '7 days'
        AND summary_date < DATE_TRUNC('week', NOW())
        AND calories_total IS NOT NULL
    `);
    const previousWeekCalories = parseInt(previousWeekCaloriesResult.rows[0].prev_week_calories);
    const caloriesChange = currentWeekCalories - previousWeekCalories;

    // Heart Rate Trend (Current 7 days vs Previous 7 days)
    const previousHeartRateResult = await query(`
      SELECT ROUND(AVG((min_hr + max_hr) / 2)) as avg_hr
      FROM daily_summaries 
      WHERE summary_date >= NOW() - INTERVAL '14 days'
        AND summary_date < NOW() - INTERVAL '7 days'
        AND min_hr IS NOT NULL AND max_hr IS NOT NULL
    `);
    const previousAvgHeartRate = previousHeartRateResult.rows[0]?.avg_hr || 0;
    const heartRateChange = (avgHeartRate || 0) - previousAvgHeartRate;

    // Stress Level Trend (Current 7 days vs Previous 7 days)
    const previousStressResult = await query(`
      SELECT ROUND(AVG(avg_stress_level)) as avg_stress
      FROM daily_summaries 
      WHERE summary_date >= NOW() - INTERVAL '14 days'
        AND summary_date < NOW() - INTERVAL '7 days'
        AND avg_stress_level IS NOT NULL
    `);
    const previousAvgStress = previousStressResult.rows[0]?.avg_stress || 0;
    const currentAvgStress = await query(`
      SELECT ROUND(AVG(avg_stress_level)) as avg_stress
      FROM daily_summaries 
      WHERE summary_date >= NOW() - INTERVAL '7 days'
        AND avg_stress_level IS NOT NULL
    `);
    const currentStress = currentAvgStress.rows[0]?.avg_stress || 0;
    const stressChange = currentStress - previousAvgStress;

    const stats: OverviewStats = {
      totalActivities,
      avgDailySteps,
      currentWeekCalories,
      latestStressLevel,
      avgHeartRate,
      latestBodyBattery,
      avgSleepHours,
      trends: {
        stepsChange,
        caloriesChange,
        heartRateChange,
        stressChange,
      },
    };

    console.log('Overview stats calculated:', stats);

    return NextResponse.json({
      success: true,
      data: stats,
      meta: {
        timestamp: new Date().toISOString(),
        dataRange: '30 days',
      },
    });

  } catch (error) {
    console.error('Error fetching overview stats:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch overview statistics',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}