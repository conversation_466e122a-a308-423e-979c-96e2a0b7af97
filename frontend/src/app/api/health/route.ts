import { NextResponse } from 'next/server';
import { query } from '../../../lib/db';

// GET /api/health
// Basic health check endpoint for system monitoring
export async function GET() {
  try {
    // Test database connectivity
    const result = await query('SELECT NOW() as server_time, version() as db_version');
    const dbInfo = result.rows[0];

    return NextResponse.json({
      success: true,
      status: 'healthy',
      timestamp: new Date().toISOString(),
      database: {
        connected: true,
        server_time: dbInfo.server_time,
        version: dbInfo.db_version.split(' ')[0] + ' ' + dbInfo.db_version.split(' ')[1]
      },
      service: 'WearLink API'
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        status: 'error',
        timestamp: new Date().toISOString(),
        database: {
          connected: false,
          error: 'Database connection failed'
        },
        service: 'WearLink API'
      },
      { status: 503 }
    );
  }
}