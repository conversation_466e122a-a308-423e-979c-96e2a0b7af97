import { NextResponse } from 'next/server';
import { query } from '../../../../lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const metric = searchParams.get('metric') || 'all';

    console.log(`Fetching health trends for ${days} days, metric: ${metric}`);

    // Base query for health metrics
    const healthMetricsQuery = `
      SELECT 
        summary_date,
        min_hr,
        max_hr,
        avg_stress_level,
        body_battery_max,
        sleep_duration_s,
        calories_total,
        total_steps,
        processed_at
      FROM daily_summaries 
      WHERE summary_date >= NOW() - INTERVAL '${days} days'
        AND min_hr IS NOT NULL
      ORDER BY summary_date ASC
    `;

    const result = await query(healthMetricsQuery);
    const healthData = result.rows;

    // Calculate trends (current period vs previous period)
    const currentPeriodStart = new Date();
    currentPeriodStart.setDate(currentPeriodStart.getDate() - days);
    
    const previousPeriodStart = new Date();
    previousPeriodStart.setDate(previousPeriodStart.getDate() - (days * 2));
    
    const previousPeriodEnd = new Date();
    previousPeriodEnd.setDate(previousPeriodEnd.getDate() - days);

    const previousPeriodQuery = `
      SELECT 
        AVG(min_hr) as avg_min_hr,
        AVG(max_hr) as avg_max_hr,
        AVG(avg_stress_level) as avg_stress,
        AVG(body_battery_max) as avg_body_battery,
        AVG(sleep_duration_s) as avg_sleep_duration
      FROM daily_summaries 
      WHERE summary_date >= $1 
        AND summary_date <= $2
        AND min_hr IS NOT NULL
    `;

    const previousResult = await query(previousPeriodQuery, [
      previousPeriodStart.toISOString().split('T')[0],
      previousPeriodEnd.toISOString().split('T')[0]
    ]);

    // Calculate current period averages
    const currentPeriodAvg = {
      avg_min_hr: healthData.reduce((sum, row) => sum + (row.min_hr || 0), 0) / healthData.length,
      avg_max_hr: healthData.reduce((sum, row) => sum + (row.max_hr || 0), 0) / healthData.length,
      avg_stress: healthData.reduce((sum, row) => sum + (row.avg_stress_level || 0), 0) / healthData.length,
      avg_body_battery: healthData.reduce((sum, row) => sum + (row.body_battery_max || 0), 0) / healthData.length,
      avg_sleep_duration: healthData.reduce((sum, row) => sum + (row.sleep_duration_s || 0), 0) / healthData.length
    };

    // Calculate percentage changes
    const previousData = previousResult.rows[0];
    const trends = {
      min_hr_change: previousData?.avg_min_hr ? 
        ((currentPeriodAvg.avg_min_hr - previousData.avg_min_hr) / previousData.avg_min_hr * 100) : 0,
      max_hr_change: previousData?.avg_max_hr ? 
        ((currentPeriodAvg.avg_max_hr - previousData.avg_max_hr) / previousData.avg_max_hr * 100) : 0,
      stress_change: previousData?.avg_stress ? 
        ((currentPeriodAvg.avg_stress - previousData.avg_stress) / previousData.avg_stress * 100) : 0,
      body_battery_change: previousData?.avg_body_battery ? 
        ((currentPeriodAvg.avg_body_battery - previousData.avg_body_battery) / previousData.avg_body_battery * 100) : 0,
      sleep_change: previousData?.avg_sleep_duration ? 
        ((currentPeriodAvg.avg_sleep_duration - previousData.avg_sleep_duration) / previousData.avg_sleep_duration * 100) : 0
    };

    // Format data for charts
    const chartData = healthData.map(row => ({
      date: row.summary_date,
      min_hr: row.min_hr,
      max_hr: row.max_hr,
      avg_hr: row.min_hr && row.max_hr ? Math.round((row.min_hr + row.max_hr) / 2) : null,
      stress_level: row.avg_stress_level,
      body_battery: row.body_battery_max,
      sleep_hours: row.sleep_duration_s ? Math.round(row.sleep_duration_s / 3600 * 10) / 10 : null,
      calories: row.calories_total,
      steps: row.total_steps
    }));

    // Generate weekly aggregated data for better visualization
    const weeklyData: any[] = [];
    const weeksMap = new Map();

    chartData.forEach(day => {
      const date = new Date(day.date);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay()); // Get Sunday of the week
      const weekKey = weekStart.toISOString().split('T')[0];

      if (!weeksMap.has(weekKey)) {
        weeksMap.set(weekKey, {
          week_start: weekKey,
          days: [],
          min_hr_values: [],
          max_hr_values: [],
          stress_values: [],
          body_battery_values: [],
          sleep_values: []
        });
      }

      const weekData = weeksMap.get(weekKey);
      weekData.days.push(day);
      if (day.min_hr) weekData.min_hr_values.push(day.min_hr);
      if (day.max_hr) weekData.max_hr_values.push(day.max_hr);
      if (day.stress_level) weekData.stress_values.push(day.stress_level);
      if (day.body_battery) weekData.body_battery_values.push(day.body_battery);
      if (day.sleep_hours) weekData.sleep_values.push(day.sleep_hours);
    });

    // Calculate weekly averages
    Array.from(weeksMap.values()).forEach(week => {
      weeklyData.push({
        week_start: week.week_start,
        week_label: new Date(week.week_start).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        avg_min_hr: week.min_hr_values.length ? Math.round(week.min_hr_values.reduce((a, b) => a + b, 0) / week.min_hr_values.length) : null,
        avg_max_hr: week.max_hr_values.length ? Math.round(week.max_hr_values.reduce((a, b) => a + b, 0) / week.max_hr_values.length) : null,
        avg_stress: week.stress_values.length ? Math.round(week.stress_values.reduce((a, b) => a + b, 0) / week.stress_values.length) : null,
        avg_body_battery: week.body_battery_values.length ? Math.round(week.body_battery_values.reduce((a, b) => a + b, 0) / week.body_battery_values.length) : null,
        avg_sleep: week.sleep_values.length ? Math.round(week.sleep_values.reduce((a, b) => a + b, 0) / week.sleep_values.length * 10) / 10 : null,
        days_count: week.days.length
      });
    });

    return NextResponse.json({
      success: true,
      data: {
        daily_data: chartData,
        weekly_data: weeklyData.sort((a, b) => new Date(a.week_start).getTime() - new Date(b.week_start).getTime()),
        current_averages: {
          min_hr: Math.round(currentPeriodAvg.avg_min_hr),
          max_hr: Math.round(currentPeriodAvg.avg_max_hr),
          avg_hr: Math.round((currentPeriodAvg.avg_min_hr + currentPeriodAvg.avg_max_hr) / 2),
          stress_level: Math.round(currentPeriodAvg.avg_stress),
          body_battery: Math.round(currentPeriodAvg.avg_body_battery),
          sleep_hours: Math.round(currentPeriodAvg.avg_sleep_duration / 3600 * 10) / 10
        },
        trends: {
          min_hr_change: Math.round(trends.min_hr_change * 10) / 10,
          max_hr_change: Math.round(trends.max_hr_change * 10) / 10,
          stress_change: Math.round(trends.stress_change * 10) / 10,
          body_battery_change: Math.round(trends.body_battery_change * 10) / 10,
          sleep_change: Math.round(trends.sleep_change * 10) / 10
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        period_days: days,
        total_records: healthData.length,
        date_range: {
          start: healthData[0]?.summary_date,
          end: healthData[healthData.length - 1]?.summary_date
        }
      }
    });

  } catch (error) {
    console.error('Error fetching health trends:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch health trends data',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
      },
      { status: 500 }
    );
  }
}