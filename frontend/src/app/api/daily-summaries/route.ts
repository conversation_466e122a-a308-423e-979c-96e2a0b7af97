import { NextResponse } from 'next/server';
import { query } from '../../../lib/db';
import { DailySummary } from '../../../lib/db';

// GET /api/daily-summaries
// Returns time-series fitness data for charts and analysis
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    console.log(`Fetching daily summaries for ${days} days`);

    let dateFilter = '';
    let queryParams: any[] = [];

    if (startDate && endDate) {
      dateFilter = 'WHERE summary_date >= $1 AND summary_date <= $2';
      queryParams = [startDate, endDate];
    } else {
      dateFilter = `WHERE summary_date >= NOW() - INTERVAL '${days} DAY'`;
      queryParams = [];
    }

    const result = await query(`
      SELECT 
        summary_date,
        total_steps,
        total_distance_m,
        calories_total,
        calories_active,
        sleep_duration_s,
        floors_climbed,
        min_hr,
        max_hr,
        avg_stress_level,
        body_battery_max,
        processed_at
      FROM daily_summaries 
      ${dateFilter}
      ORDER BY summary_date ASC
    `, queryParams);

    const summaries: Partial<DailySummary>[] = result.rows;

    // Calculate summary statistics
    const totalDays = summaries.length;
    const avgSteps = summaries.reduce((sum, day) => sum + (day.total_steps || 0), 0) / totalDays;
    const avgCalories = summaries.reduce((sum, day) => sum + (day.calories_total || 0), 0) / totalDays;

    return NextResponse.json({
      success: true,
      data: summaries,
      meta: {
        timestamp: new Date().toISOString(),
        totalDays,
        dateRange: {
          start: summaries[0]?.summary_date,
          end: summaries[summaries.length - 1]?.summary_date
        },
        averages: {
          steps: Math.round(avgSteps),
          calories: Math.round(avgCalories)
        }
      },
    });

  } catch (error) {
    console.error('Error fetching daily summaries:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch daily summaries',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
      },
      { status: 500 }
    );
  }
}