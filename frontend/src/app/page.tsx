"use client";

import { useState, useEffect } from "react";
import {
  TrendingUp,
  TrendingDown,
  Heart,
  Footprints,
  Flame,
  Zap,
  Activity,
  Battery,
  Moon,
} from "lucide-react";
import Sidebar from "../components/Sidebar";
import StepsLineChart from "../components/charts/StepsLineChart";
import WeeklyActivityChart from "../components/charts/WeeklyActivityChart";
import HealthMetricsChart from "../components/charts/HealthMetricsChart";
import PerformanceAnalyticsChart from "../components/charts/PerformanceAnalyticsChart";

// TypeScript interfaces for API responses
interface OverviewStats {
  totalActivities: number;
  avgDailySteps: number;
  currentWeekCalories: number;
  latestStressLevel: number | null;
  avgHeartRate: number | null;
  latestBodyBattery: number | null;
  avgSleepHours: number | null;
  trends: {
    stepsChange: number;
    caloriesChange: number;
    heartRateChange: number;
    stressChange: number;
  };
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  meta?: Record<string, unknown>;
  error?: string;
}

interface SyncStatus {
  service_status: "healthy" | "warning" | "error";
  last_activity_sync: string | null;
  last_summary_sync: string | null;
  total_activities: number;
  total_summaries: number;
  recent_sync_stats: {
    activities_last_24h: number;
    summaries_last_7days: number;
  };
}

// Dark Theme Stat Card Component
function StatCard({
  title,
  value,
  icon: Icon,
  trend,
  color = "blue",
}: {
  title: string;
  value: number | string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: number;
  color?: "blue" | "green" | "orange" | "red" | "gray";
}) {
  const colorClasses = {
    blue: "bg-blue-500/10 border-blue-500/20",
    green: "bg-emerald-500/10 border-emerald-500/20",
    orange: "bg-amber-500/10 border-amber-500/20",
    red: "bg-red-500/10 border-red-500/20",
    gray: "bg-slate-500/10 border-slate-500/20",
  };

  const iconColors = {
    blue: "text-blue-400",
    green: "text-emerald-400",
    orange: "text-amber-400",
    red: "text-red-400",
    gray: "text-slate-400",
  };

  return (
    <div
      className={`metric-card p-6 rounded-xl ${colorClasses[color]} fade-in`}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-slate-400 mb-1">{title}</p>
          <p className="text-2xl font-bold text-slate-50">{value}</p>
          {trend !== undefined && (
            <div className="flex items-center mt-2">
              {trend > 0 ? (
                <TrendingUp className="w-4 h-4 text-emerald-400 mr-1" />
              ) : trend < 0 ? (
                <TrendingDown className="w-4 h-4 text-red-400 mr-1" />
              ) : null}
              <span
                className={`text-sm font-medium ${
                  trend > 0
                    ? "text-emerald-400"
                    : trend < 0
                    ? "text-red-400"
                    : "text-slate-400"
                }`}
              >
                {trend > 0 ? "+" : ""}
                {trend}%
              </span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg bg-slate-800/50 ${iconColors[color]}`}>
          <Icon className="w-6 h-6" />
        </div>
      </div>
    </div>
  );
}

// Dark Theme Loading Skeleton Component
function StatCardSkeleton() {
  return (
    <div className="metric-card p-6 rounded-xl bg-slate-800/50 border-slate-700/50 animate-pulse">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="h-4 bg-slate-700 rounded w-24 mb-2"></div>
          <div className="h-8 bg-slate-700 rounded w-16 mb-2"></div>
          <div className="h-4 bg-slate-700 rounded w-12"></div>
        </div>
        <div className="w-12 h-12 bg-slate-700 rounded-lg"></div>
      </div>
    </div>
  );
}

export default function Dashboard() {
  const [stats, setStats] = useState<OverviewStats | null>(null);
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Data fetching with auto-refresh
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        console.log("Fetching dashboard data...");

        // Fetch both stats and sync status in parallel
        const [statsResponse, syncResponse] = await Promise.all([
          fetch("/api/stats/overview"),
          fetch("/api/sync/status"),
        ]);

        const statsResult: ApiResponse<OverviewStats> =
          await statsResponse.json();
        const syncResult: ApiResponse<SyncStatus> = await syncResponse.json();

        if (statsResult.success) {
          setStats(statsResult.data);
          console.log("Stats loaded:", statsResult.data);
        } else {
          setError(statsResult.error || "Failed to load stats");
        }

        if (syncResult.success) {
          setSyncStatus(syncResult.data);
          console.log("Sync status loaded:", syncResult.data);
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        setError("Network error - unable to fetch data");
      } finally {
        setLoading(false);
      }
    }

    fetchData();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  // Format numbers for display
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getSyncStatusLabel = (status: "healthy" | "warning" | "error") => {
    switch (status) {
      case "healthy":
        return "Active";
      case "warning":
        return "Delayed";
      case "error":
        return "Failed";
    }
  };

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Sidebar */}
      <Sidebar
        activeTab={activeTab}
        onTabChange={setActiveTab}
        collapsed={sidebarCollapsed}
        onToggleCollapsed={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      {/* Main Content Area */}
      <div
        className={`w-full transition-all duration-300 ${
          sidebarCollapsed ? "md:ml-20" : "md:ml-72"
        }`}
      >
        {/* Header */}
        <header className="bg-slate-800/50 border-b border-slate-700">
          <div className="px-6 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-slate-50">Overview</h1>
                <p className="text-slate-400 mt-1">
                  Complete overview of your fitness metrics
                </p>
              </div>

              {/* Top-right metrics */}
              <div className="flex items-center space-x-8">
                <div className="text-right">
                  <p className="text-2xl font-bold text-blue-400">
                    {stats ? formatNumber(stats.avgDailySteps) : "---"}
                  </p>
                  <p className="text-sm text-slate-400">Steps Today</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-emerald-400">89</p>
                  <p className="text-sm text-slate-400">Active Min</p>
                </div>
                {syncStatus && (
                  <div
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      syncStatus.service_status === "healthy"
                        ? "bg-emerald-500/20 text-emerald-400"
                        : syncStatus.service_status === "warning"
                        ? "bg-amber-500/20 text-amber-400"
                        : "bg-red-500/20 text-red-400"
                    }`}
                  >
                    Sync: {getSyncStatusLabel(syncStatus.service_status)}
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="p-6">
          {/* Error State */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 text-red-400 px-4 py-3 rounded-lg mb-6">
              <strong>Error:</strong> {error}
            </div>
          )}

          {/* Primary Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {loading ? (
              // Loading State
              <>
                <StatCardSkeleton />
                <StatCardSkeleton />
                <StatCardSkeleton />
                <StatCardSkeleton />
              </>
            ) : stats ? (
              // Loaded State with Real Data
              <>
                <StatCard
                  title="Daily Steps"
                  value={formatNumber(stats.avgDailySteps)}
                  icon={Footprints}
                  trend={Math.round(
                    (stats.trends.stepsChange / stats.avgDailySteps) * 100
                  )}
                  color="blue"
                />
                <StatCard
                  title="Heart Rate"
                  value={
                    stats.avgHeartRate ? `${stats.avgHeartRate} bpm` : "---"
                  }
                  icon={Heart}
                  trend={
                    stats.trends.heartRateChange
                      ? Math.round(stats.trends.heartRateChange)
                      : undefined
                  }
                  color="red"
                />
                <StatCard
                  title="Stress Level"
                  value={
                    stats.latestStressLevel
                      ? `${stats.latestStressLevel}/100`
                      : "---"
                  }
                  icon={Activity}
                  trend={
                    stats.trends.stressChange
                      ? Math.round(stats.trends.stressChange)
                      : undefined
                  }
                  color="orange"
                />
                <StatCard
                  title="Sleep Hours"
                  value={
                    stats.avgSleepHours ? `${stats.avgSleepHours}h` : "---"
                  }
                  icon={Moon}
                  trend={undefined}
                  color="blue"
                />
              </>
            ) : null}
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Daily Steps Trend Chart */}
            <div className="lg:col-span-2">
              <StepsLineChart />
            </div>

            {/* Weekly Activity Chart */}
            <div>
              <WeeklyActivityChart />
            </div>
          </div>

          {/* Health Metrics Section */}
          <div className="grid grid-cols-1 lg:grid-cols-1 gap-6 mb-8">
            <div className="lg:col-span-1">
              <HealthMetricsChart />
            </div>
          </div>

          {/* Performance Analytics Section */}
          <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
            <div className="lg:col-span-1">
              <PerformanceAnalyticsChart />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
