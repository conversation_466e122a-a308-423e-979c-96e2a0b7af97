@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark theme color variables */
:root {
  --bg-primary: #0f172a;      /* slate-900 */
  --bg-secondary: #1e293b;    /* slate-800 */
  --bg-tertiary: #334155;     /* slate-700 */
  --text-primary: #f8fafc;    /* slate-50 */
  --text-secondary: #cbd5e1;  /* slate-300 */
  --text-muted: #94a3b8;      /* slate-400 */
  --accent-purple: #8b5cf6;   /* violet-500 */
  --accent-blue: #3b82f6;     /* blue-500 */
  --accent-green: #10b981;    /* emerald-500 */
  --accent-orange: #f59e0b;   /* amber-500 */
  --accent-red: #ef4444;      /* red-500 */
  --border-color: #475569;    /* slate-600 */
}

/* Base dark theme styles */
html {
  color-scheme: dark;
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-color);
}

/* Chart and data visualization styles */
.chart-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
}

.metric-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.metric-card:hover {
  border-color: var(--accent-purple);
  transform: translateY(-2px);
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}