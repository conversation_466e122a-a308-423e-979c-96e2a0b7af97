import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "WearLink - Fitness Analytics Dashboard",
  description: "Professional fitness analytics dashboard with comprehensive health data visualization",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className="antialiased bg-slate-900 text-slate-50" suppressHydrationWarning>
        {children}
      </body>
    </html>
  );
}