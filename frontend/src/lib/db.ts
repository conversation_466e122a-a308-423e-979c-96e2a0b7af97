import { Pool } from 'pg';

// Database connection pool
let pool: Pool;

export function getPool(): Pool {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    pool.on('connect', () => {
      console.log('Connected to PostgreSQL database');
    });

    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
    });
  }

  return pool;
}

// Helper function to execute queries
export async function query(text: string, params?: unknown[]) {
  const pool = getPool();
  const start = Date.now();
  
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Executed query', { text, duration, rows: result.rowCount });
    }
    
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

// Type definitions for health data
export interface DailySummary {
  summary_date: string;
  user_id: string;
  total_steps: number;
  total_distance_m: number;
  calories_total: number;
  calories_active: number;
  sleep_duration_s: number;
  floors_climbed: number;
  min_hr: number;
  max_hr: number;
  avg_stress_level: number;
  body_battery_max: number;
  processed_at: string;
  raw_data: Record<string, unknown>;
}

export interface Activity {
  activity_id: string;
  user_id: string;
  activity_type: string;
  start_time_gmt: string;
  distance_km: number;
  duration_s: number;
  average_hr: number;
  max_hr: number;
  calories: number;
  processed_at: string;
  raw_data: Record<string, unknown>;
}