-- WearLink Database Schema
-- Universal health and fitness data structure for multiple wearable devices

-- Activities table: stores individual workout/activity data
CREATE TABLE IF NOT EXISTS activities (
    activity_id BIGINT PRIMARY KEY,
    user_id VARCHAR(255),
    activity_type VARCHAR(50),
    start_time_gmt TIMESTAMP WITH TIME ZONE,
    distance_km NUMERIC(10, 3),
    duration_s NUMERIC(10, 2),
    average_hr SMALLINT,
    max_hr SMALLINT,
    calories INTEGER,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    raw_data JSONB
);

-- Daily summaries table: stores daily fitness metrics and health data
CREATE TABLE IF NOT EXISTS daily_summaries (
    summary_date DATE PRIMARY KEY,
    user_id VARCHAR(255),
    total_steps INTEGER,
    total_distance_m INTEGER,
    calories_total INTEGER,
    calories_active INTEGER,
    sleep_duration_s INTEGER,
    floors_climbed INTEGER,
    min_hr SMALLINT,
    max_hr SMALLINT,
    avg_stress_level SMALLINT,
    body_battery_max SMALLINT,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    raw_data JSONB
);

-- Sleep detailed analysis table: stores comprehensive sleep data
CREATE TABLE IF NOT EXISTS sleep_data (
    sleep_date DATE PRIMARY KEY,
    user_id VARCHAR(255),
    sleep_start_time TIMESTAMP WITH TIME ZONE,
    sleep_end_time TIMESTAMP WITH TIME ZONE,
    total_sleep_time_s INTEGER,
    deep_sleep_s INTEGER,
    light_sleep_s INTEGER,
    rem_sleep_s INTEGER,
    awake_time_s INTEGER,
    sleep_score INTEGER,
    sleep_efficiency DECIMAL(5,2),
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    raw_data JSONB
);

-- Heart Rate Variability table: stores HRV measurements for recovery analysis
CREATE TABLE IF NOT EXISTS hrv_data (
    measurement_date DATE PRIMARY KEY,
    user_id VARCHAR(255),
    hrv_rmssd DECIMAL(8,2),
    hrv_sdrr DECIMAL(8,2),
    baseline_balanced_lower INTEGER,
    baseline_balanced_upper INTEGER,
    status VARCHAR(20), -- balanced/unbalanced/poor
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    raw_data JSONB
);

-- Body composition table: stores weight and body composition measurements
CREATE TABLE IF NOT EXISTS body_composition (
    measurement_date DATE PRIMARY KEY,
    user_id VARCHAR(255),
    weight_kg DECIMAL(5,2),
    body_fat_percentage DECIMAL(5,2),
    muscle_mass_kg DECIMAL(5,2),
    bone_mass_kg DECIMAL(5,2),
    body_water_percentage DECIMAL(5,2),
    bmi DECIMAL(5,2),
    metabolic_age INTEGER,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    raw_data JSONB
);

-- Blood oxygen (SpO2) table: stores oxygen saturation measurements
CREATE TABLE IF NOT EXISTS spo2_data (
    measurement_date DATE PRIMARY KEY,
    user_id VARCHAR(255),
    avg_spo2_percentage DECIMAL(5,2),
    lowest_spo2_percentage DECIMAL(5,2),
    latest_spo2_percentage DECIMAL(5,2),
    spo2_readings JSONB, -- hourly readings array
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    raw_data JSONB
);

-- Training & performance metrics table: stores fitness and training data
CREATE TABLE IF NOT EXISTS training_metrics (
    measurement_date DATE PRIMARY KEY,
    user_id VARCHAR(255),
    vo2_max DECIMAL(5,2),
    fitness_age INTEGER,
    training_load_7_day INTEGER,
    training_load_4_week INTEGER,
    training_status VARCHAR(50),
    training_readiness_score INTEGER,
    race_pred_5k_seconds INTEGER,
    race_pred_10k_seconds INTEGER,
    race_pred_half_marathon_seconds INTEGER,
    race_pred_marathon_seconds INTEGER,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    raw_data JSONB
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_activities_start_time ON activities(start_time_gmt);
CREATE INDEX IF NOT EXISTS idx_activities_user_id ON activities(user_id);
CREATE INDEX IF NOT EXISTS idx_activities_type ON activities(activity_type);

CREATE INDEX IF NOT EXISTS idx_daily_summaries_date ON daily_summaries(summary_date);
CREATE INDEX IF NOT EXISTS idx_daily_summaries_user_id ON daily_summaries(user_id);

CREATE INDEX IF NOT EXISTS idx_sleep_data_date ON sleep_data(sleep_date);
CREATE INDEX IF NOT EXISTS idx_sleep_data_user ON sleep_data(user_id);

CREATE INDEX IF NOT EXISTS idx_hrv_data_date ON hrv_data(measurement_date);
CREATE INDEX IF NOT EXISTS idx_hrv_data_user ON hrv_data(user_id);

CREATE INDEX IF NOT EXISTS idx_body_composition_date ON body_composition(measurement_date);
CREATE INDEX IF NOT EXISTS idx_body_composition_user ON body_composition(user_id);

CREATE INDEX IF NOT EXISTS idx_spo2_data_date ON spo2_data(measurement_date);
CREATE INDEX IF NOT EXISTS idx_spo2_data_user ON spo2_data(user_id);

CREATE INDEX IF NOT EXISTS idx_training_metrics_date ON training_metrics(measurement_date);
CREATE INDEX IF NOT EXISTS idx_training_metrics_user ON training_metrics(user_id);

-- Comments for documentation
COMMENT ON TABLE activities IS 'Individual fitness activities synced from wearable devices';
COMMENT ON TABLE daily_summaries IS 'Daily aggregated fitness and health metrics';
COMMENT ON TABLE sleep_data IS 'Detailed sleep analysis including sleep stages and quality scores';
COMMENT ON TABLE hrv_data IS 'Heart Rate Variability measurements for recovery monitoring';
COMMENT ON TABLE body_composition IS 'Body composition measurements including weight, body fat, and muscle mass';
COMMENT ON TABLE spo2_data IS 'Blood oxygen saturation (SpO2) measurements throughout the day';
COMMENT ON TABLE training_metrics IS 'Training and performance metrics including VO2 Max, fitness age, and race predictions';