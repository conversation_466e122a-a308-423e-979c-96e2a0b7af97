# WearLink - Production Environment Configuration
# Copy this file to .env and update with your production values

# Database Configuration - Production
POSTGRES_DB=wearlink_production
POSTGRES_USER=wearlink_production_user
POSTGRES_PASSWORD=CHANGE_THIS_SECURE_PASSWORD_IN_PRODUCTION

# Garmin Connect Credentials - Production
GARMIN_EMAIL=<EMAIL>
GARMIN_PASSWORD=your_production_garmin_password

# Application Settings - Production
NODE_ENV=production
LOG_LEVEL=info
APP_PORT=3001

# Security Configuration
SESSION_SECRET=CHANGE_THIS_TO_RANDOM_32_CHAR_STRING
JWT_SECRET=CHANGE_THIS_TO_RANDOM_64_CHAR_STRING
CORS_ORIGIN=https://your-domain.com

# SSL/TLS Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/wearlink.crt
SSL_KEY_PATH=/etc/ssl/private/wearlink.key

# Performance & Scaling
DATABASE_POOL_SIZE=20
DATABASE_MAX_CONNECTIONS=100
CACHE_TTL_SECONDS=300
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring & Logging
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_FORMAT=json
LOG_FILE_PATH=/var/log/wearlink/app.log
SENTRY_DSN=https://<EMAIL>/project-id

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=wearlink-backups
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Sync Configuration
SYNC_INTERVAL_HOURS=1
SYNC_RETRY_ATTEMPTS=5
SYNC_RETRY_DELAY_SECONDS=300

# Redis Cache (Optional)
REDIS_ENABLED=false
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password