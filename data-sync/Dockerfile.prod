FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r wearlink && useradd -r -g wearlink wearlink

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY sync_garmin.py .
COPY smart_sync_utils.py .

# Create log directory
RUN mkdir -p /var/log/wearlink && chown wearlink:wearlink /var/log/wearlink

# Switch to non-root user
USER wearlink

# Health check
HEALTHCHECK --interval=5m --timeout=30s --start-period=1m --retries=3 \
    CMD python -c "import psycopg2; psycopg2.connect(host='postgres', dbname='wearlink_production', user='wearlink_production_user')" || exit 1

# Run the sync service
CMD ["python", "sync_garmin.py"]