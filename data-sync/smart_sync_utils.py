"""
Smart Sync Utilities for WearLink Platform
Provides intelligent synchronization logic with data validation,
conflict resolution, and error recovery mechanisms.
"""

import logging
import json
from datetime import date, datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import psycopg2
from psycopg2 import OperationalError


class SyncValidator:
    """Validates data integrity before database insertion"""
    
    @staticmethod
    def validate_daily_summary(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate daily summary data"""
        errors = []
        
        # Required fields
        if not data.get('summary_date'):
            errors.append("Missing required field: summary_date")
        
        # Data type validation
        if data.get('total_steps') is not None and not isinstance(data.get('total_steps'), int):
            errors.append("total_steps must be an integer")
            
        # Range validation
        if data.get('total_steps') is not None and data.get('total_steps') < 0:
            errors.append("total_steps cannot be negative")
            
        if data.get('calories_total') is not None and data.get('calories_total') < 0:
            errors.append("calories_total cannot be negative")
            
        # Heart rate validation
        if data.get('max_hr') is not None and (data.get('max_hr') < 50 or data.get('max_hr') > 250):
            errors.append("max_hr outside reasonable range (50-250)")
            
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_activity(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate activity data"""
        errors = []
        
        if not data.get('activity_id'):
            errors.append("Missing required field: activity_id")
            
        if data.get('distance_m') is not None and data.get('distance_m') < 0:
            errors.append("distance_m cannot be negative")
            
        if data.get('duration_s') is not None and data.get('duration_s') <= 0:
            errors.append("duration_s must be positive")
            
        return len(errors) == 0, errors


class ConflictResolver:
    """Handles data conflicts during synchronization"""
    
    @staticmethod
    def resolve_daily_summary_conflict(existing_data: Dict, new_data: Dict) -> Dict:
        """Resolve conflicts for daily summary data"""
        resolved = existing_data.copy()
        
        # Take non-null values from new data if existing is null
        for key in ['total_steps', 'calories_total', 'sleep_duration_s']:
            if resolved.get(key) is None and new_data.get(key) is not None:
                resolved[key] = new_data[key]
                
        # For critical metrics, prefer newer data if significantly different
        if (new_data.get('total_steps') and existing_data.get('total_steps') and 
            abs(new_data['total_steps'] - existing_data['total_steps']) > 1000):
            logging.info(f"Significant step difference detected: {existing_data['total_steps']} -> {new_data['total_steps']}")
            resolved['total_steps'] = new_data['total_steps']
            
        return resolved


class SmartSyncManager:
    """Manages intelligent synchronization with retry logic and data validation"""
    
    def __init__(self, conn):
        self.conn = conn
        self.validator = SyncValidator()
        self.resolver = ConflictResolver()
        self.retry_attempts = 3
        self.retry_delay = 5  # seconds
    
    def get_optimal_sync_window(self, data_type: str) -> Tuple[date, date]:
        """Calculate optimal sync window based on data type and history"""
        with self.conn.cursor() as cursor:
            if data_type == 'daily_summaries':
                cursor.execute("SELECT MAX(summary_date) FROM daily_summaries;")
                result = cursor.fetchone()
                last_sync = result[0] if result and result[0] else None
            elif data_type == 'activities':
                cursor.execute("SELECT MAX(start_time::date) FROM activities;")
                result = cursor.fetchone()
                last_sync = result[0] if result and result[0] else None
            else:
                last_sync = None
                
        # Smart window calculation
        if last_sync:
            # Sync from last known date, but include buffer for late data
            start_date = last_sync - timedelta(days=2)  # 2-day buffer
        else:
            # First sync - get last 30 days
            start_date = date.today() - timedelta(days=30)
            
        # End date is yesterday to avoid partial data
        end_date = date.today() - timedelta(days=1)
        
        return start_date, end_date
    
    def sync_with_validation(self, sync_function, data_type: str, *args, **kwargs):
        """Execute sync function with validation and retry logic"""
        for attempt in range(self.retry_attempts):
            try:
                logging.info(f"Sync attempt {attempt + 1}/{self.retry_attempts} for {data_type}")
                
                # Execute the sync function
                result = sync_function(*args, **kwargs)
                
                # Validate sync success
                if self.validate_sync_success(data_type):
                    logging.info(f"Successfully synced {data_type}")
                    return result
                else:
                    logging.warning(f"Sync validation failed for {data_type}")
                    
            except Exception as e:
                logging.error(f"Sync attempt {attempt + 1} failed for {data_type}: {str(e)}")
                if attempt < self.retry_attempts - 1:
                    import time
                    time.sleep(self.retry_delay)
                else:
                    logging.error(f"All sync attempts failed for {data_type}")
                    raise
    
    def validate_sync_success(self, data_type: str) -> bool:
        """Validate that sync was successful"""
        try:
            with self.conn.cursor() as cursor:
                if data_type == 'daily_summaries':
                    cursor.execute("SELECT COUNT(*) FROM daily_summaries WHERE processed_at > NOW() - INTERVAL '1 hour';")
                elif data_type == 'activities':
                    cursor.execute("SELECT COUNT(*) FROM activities WHERE processed_at > NOW() - INTERVAL '1 hour';")
                else:
                    return True
                    
                result = cursor.fetchone()
                return result[0] > 0 if result else False
        except Exception as e:
            logging.error(f"Sync validation error: {e}")
            return False
    
    def clean_stale_data(self, days_threshold: int = 90):
        """Clean up stale or corrupted data"""
        try:
            with self.conn.cursor() as cursor:
                # Remove entries older than threshold with null critical fields
                cursor.execute("""
                    DELETE FROM daily_summaries 
                    WHERE summary_date < %s 
                    AND total_steps IS NULL 
                    AND calories_total IS NULL;
                """, (date.today() - timedelta(days=days_threshold),))
                
                deleted_count = cursor.rowcount
                self.conn.commit()
                
                if deleted_count > 0:
                    logging.info(f"Cleaned up {deleted_count} stale daily summary records")
                    
        except Exception as e:
            logging.error(f"Error cleaning stale data: {e}")
            self.conn.rollback()
    
    def get_sync_statistics(self) -> Dict[str, Any]:
        """Get synchronization statistics for monitoring"""
        stats = {}
        
        try:
            with self.conn.cursor() as cursor:
                # Daily summaries stats
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_records,
                        MAX(summary_date) as latest_date,
                        COUNT(*) FILTER (WHERE processed_at > NOW() - INTERVAL '24 hours') as synced_last_24h
                    FROM daily_summaries;
                """)
                result = cursor.fetchone()
                stats['daily_summaries'] = {
                    'total_records': result[0],
                    'latest_date': result[1].isoformat() if result[1] else None,
                    'synced_last_24h': result[2]
                }
                
                # Activities stats
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_records,
                        MAX(start_time::date) as latest_date,
                        COUNT(*) FILTER (WHERE processed_at > NOW() - INTERVAL '24 hours') as synced_last_24h
                    FROM activities;
                """)
                result = cursor.fetchone()
                stats['activities'] = {
                    'total_records': result[0],
                    'latest_date': result[1].isoformat() if result[1] else None,
                    'synced_last_24h': result[2]
                }
                
        except Exception as e:
            logging.error(f"Error getting sync statistics: {e}")
            
        return stats


def get_smart_sync_end_date() -> date:
    """
    Calculate smart end date for sync operations.
    Ensures we don't sync partial data from the current day.
    """
    now = datetime.now()
    
    # If it's before 6 AM, consider the previous day as complete
    if now.hour < 6:
        return (now - timedelta(days=1)).date()
    else:
        # Otherwise, sync up to yesterday
        return (now - timedelta(days=1)).date()


def validate_data_integrity(conn, table_name: str) -> Dict[str, Any]:
    """Validate data integrity for a specific table"""
    integrity_report = {
        'table': table_name,
        'issues': [],
        'total_records': 0,
        'valid_records': 0
    }
    
    try:
        with conn.cursor() as cursor:
            if table_name == 'daily_summaries':
                # Check for duplicates
                cursor.execute("""
                    SELECT summary_date, COUNT(*) 
                    FROM daily_summaries 
                    GROUP BY summary_date 
                    HAVING COUNT(*) > 1;
                """)
                duplicates = cursor.fetchall()
                if duplicates:
                    integrity_report['issues'].append(f"Found {len(duplicates)} duplicate dates")
                
                # Check for impossible values
                cursor.execute("""
                    SELECT COUNT(*) FROM daily_summaries 
                    WHERE total_steps > 100000 OR total_steps < 0;
                """)
                result = cursor.fetchone()
                if result[0] > 0:
                    integrity_report['issues'].append(f"Found {result[0]} records with impossible step counts")
                
                # Get totals
                cursor.execute("SELECT COUNT(*) FROM daily_summaries;")
                integrity_report['total_records'] = cursor.fetchone()[0]
                
                cursor.execute("""
                    SELECT COUNT(*) FROM daily_summaries 
                    WHERE total_steps IS NOT NULL AND total_steps >= 0 AND total_steps <= 100000;
                """)
                integrity_report['valid_records'] = cursor.fetchone()[0]
                
    except Exception as e:
        integrity_report['issues'].append(f"Error during validation: {str(e)}")
    
    return integrity_report