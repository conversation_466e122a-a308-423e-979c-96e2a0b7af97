import os
import logging
import json
import time
import argparse
from datetime import date, timedelta, datetime

from dotenv import load_dotenv
from garminconnect import Garmin
import psycopg2
from psycopg2 import OperationalError
from smart_sync_utils import SmartSyncManager, get_smart_sync_end_date, validate_data_integrity

# Configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
load_dotenv()

# Database Connection
def get_db_connection():
    """Establishes a connection to the PostgreSQL database, retrying on failure."""
    conn = None
    retries = 5
    while retries > 0 and conn is None:
        try:
            conn = psycopg2.connect(
                host=os.getenv("DB_HOST"), 
                dbname=os.getenv("DB_NAME"), 
                user=os.getenv("DB_USER"), 
                password=os.getenv("DB_PASS")
            )
            logging.info("Database connection successful.")
        except OperationalError as e:
            retries -= 1
            logging.warning(f"Could not connect to database, retrying in 10 seconds... ({retries} retries left). Error: {e}")
            time.sleep(10)
    return conn

def init_db(conn):
    """Initializes required database tables if they don't exist."""
    with conn.cursor() as cursor:
        # Tables will be initialized by PostgreSQL init script
        # Just verify they exist by checking one table
        cursor.execute("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'activities');")
        tables_exist = cursor.fetchone()[0]
        
        if tables_exist:
            logging.info("Database tables already initialized.")
        else:
            logging.warning("Database tables not found. They should be created by PostgreSQL init script.")
        conn.commit()

# Sync Activities
def sync_activities(client, conn):
    logging.info("--- Starting Activity Sync ---")
    with conn.cursor() as cursor:
        cursor.execute("SELECT activity_id FROM activities ORDER BY start_time_gmt DESC LIMIT 1;")
        result = cursor.fetchone()
        latest_known_id = result[0] if result else None
        logging.info(f"Latest activity ID in DB: {latest_known_id or 'None (full sync)'}")

        start, limit = 0, 50
        activities_to_process = []
        
        while True:
            logging.info(f"Fetching activities from index {start} to {start + limit}...")
            activities = client.get_activities(start, limit)
            if not activities:
                logging.info("No more activities found.")
                break
            
            found_known_activity = False
            for activity in activities:
                if activity["activityId"] == latest_known_id:
                    logging.info(f"Found known activity ID {activity['activityId']}. Stopping fetch.")
                    found_known_activity = True
                    break
                activities_to_process.append(activity)
            
            if found_known_activity:
                break
            start += limit
        
        if activities_to_process:
            logging.info(f"Found {len(activities_to_process)} new activities to sync.")
            for activity in reversed(activities_to_process):
                data_to_insert = {
                    "activity_id": activity.get("activityId"),
                    "user_id": os.getenv("GARMIN_EMAIL"),
                    "activity_type": activity.get("activityType", {}).get("typeKey"),
                    "start_time_gmt": activity.get("startTimeGMT"),
                    "distance_km": activity.get("distance", 0) / 1000.0,
                    "duration_s": activity.get("duration"),
                    "average_hr": activity.get("averageHR"),
                    "max_hr": activity.get("maxHR"),
                    "calories": activity.get("calories"),
                    "raw_data": json.dumps(activity)
                }
                sql = """
                INSERT INTO activities (activity_id, user_id, activity_type, start_time_gmt, distance_km, duration_s, average_hr, max_hr, calories, raw_data)
                VALUES (%(activity_id)s, %(user_id)s, %(activity_type)s, %(start_time_gmt)s, %(distance_km)s, %(duration_s)s, %(average_hr)s, %(max_hr)s, %(calories)s, %(raw_data)s)
                ON CONFLICT (activity_id) DO UPDATE SET
                    user_id = EXCLUDED.user_id, activity_type = EXCLUDED.activity_type, start_time_gmt = EXCLUDED.start_time_gmt,
                    distance_km = EXCLUDED.distance_km, duration_s = EXCLUDED.duration_s, average_hr = EXCLUDED.average_hr,
                    max_hr = EXCLUDED.max_hr, calories = EXCLUDED.calories, processed_at = NOW(), raw_data = EXCLUDED.raw_data;
                """
                cursor.execute(sql, data_to_insert)
            conn.commit()
            logging.info("Successfully synced new activities.")
        else:
            logging.info("No new activities to sync.")

# Historical Sync Daily Summaries
def sync_daily_summaries_historical(client, conn, historical_days=365):
    """Sync daily summaries going back a specified number of days."""
    logging.info(f"--- Starting Historical Daily Summary Sync ({historical_days} days) ---")
    
    start_date = date.today() - timedelta(days=historical_days)
    end_date = date.today() - timedelta(days=1)  # Yesterday
    
    logging.info(f"Fetching historical daily summaries from {start_date} to {end_date}")
    
    with conn.cursor() as cursor:
        # Check if we already have data in this range
        cursor.execute("""
            SELECT COUNT(*), MIN(summary_date), MAX(summary_date) 
            FROM daily_summaries 
            WHERE summary_date BETWEEN %s AND %s
        """, (start_date, end_date))
        existing_count, min_date, max_date = cursor.fetchone()
        
        if existing_count > 0:
            logging.info(f"Found {existing_count} existing records from {min_date} to {max_date}")
            logging.info("Filling in any gaps in historical data...")
        
        current_date = start_date
        synced_count = 0
        skipped_count = 0
        error_count = 0
        
        while current_date <= end_date:
            try:
                # Check if we already have data for this date
                cursor.execute("SELECT COUNT(*) FROM daily_summaries WHERE summary_date = %s", (current_date,))
                if cursor.fetchone()[0] > 0:
                    skipped_count += 1
                    current_date += timedelta(days=1)
                    continue
                
                # Add small delay to avoid rate limiting
                time.sleep(0.5)
                
                summary = client.get_stats_and_body(current_date.isoformat())
                
                if summary:
                    # Insert daily summary data
                    cursor.execute("""
                        INSERT INTO daily_summaries (
                            summary_date, total_steps, total_distance_m, calories_total, 
                            calories_active, sleep_duration_s, floors_climbed, 
                            min_hr, max_hr, avg_stress_level, body_battery_max, processed_at
                        )
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (summary_date) DO UPDATE SET
                            total_steps = EXCLUDED.total_steps,
                            total_distance_m = EXCLUDED.total_distance_m,
                            calories_total = EXCLUDED.calories_total,
                            calories_active = EXCLUDED.calories_active,
                            sleep_duration_s = EXCLUDED.sleep_duration_s,
                            floors_climbed = EXCLUDED.floors_climbed,
                            min_hr = EXCLUDED.min_hr,
                            max_hr = EXCLUDED.max_hr,
                            avg_stress_level = EXCLUDED.avg_stress_level,
                            body_battery_max = EXCLUDED.body_battery_max,
                            processed_at = EXCLUDED.processed_at
                    """, (
                        current_date,
                        summary.get('totalSteps'),
                        summary.get('totalDistanceMeters'),
                        summary.get('totalKilocalories'),
                        summary.get('activeKilocalories'),
                        summary.get('sleepingSeconds'),
                        summary.get('floorsAscended'),
                        summary.get('minHeartRate'),
                        summary.get('maxHeartRate'),
                        summary.get('averageStressLevel'),
                        summary.get('maxBodyBattery'),
                        datetime.now()
                    ))
                    synced_count += 1
                    
                    if synced_count % 10 == 0:
                        logging.info(f"Historical sync progress: {synced_count} days processed")
                        conn.commit()  # Commit every 10 records
                
            except Exception as e:
                error_count += 1
                logging.warning(f"Failed to sync data for {current_date}: {str(e)}")
                
                # Continue with next date instead of failing completely
                if error_count > 10:
                    logging.error("Too many consecutive errors. Stopping historical sync.")
                    break
            
            current_date += timedelta(days=1)
        
        conn.commit()
        logging.info(f"Historical sync completed: {synced_count} new records, {skipped_count} skipped, {error_count} errors")
        
        return synced_count

# Sync Daily Summaries (Regular Mode)
def sync_daily_summaries(client, conn):
    logging.info("--- Starting Daily Summary Sync ---")
    
    # Initialize smart sync manager
    smart_sync = SmartSyncManager(conn)
    
    # Get optimal sync window
    start_date, end_date = smart_sync.get_optimal_sync_window('daily_summaries')
    
    with conn.cursor() as cursor:
        cursor.execute("SELECT MAX(summary_date) FROM daily_summaries;")
        result = cursor.fetchone()
        start_date = result[0] + timedelta(days=1) if result and result[0] else date.today() - timedelta(days=30)
        end_date = date.today() - timedelta(days=1)  # Yesterday

        if start_date > end_date:
            logging.info("Daily summaries are already up-to-date.")
            return

        logging.info(f"Fetching daily summaries from {start_date} to {end_date}")
        
        current_date = start_date
        synced_count = 0
        
        while current_date <= end_date:
            try:
                summary = client.get_stats_and_body(current_date.isoformat())
                
                data_to_insert = {
                    "summary_date": summary.get("calendarDate"),
                    "user_id": os.getenv("GARMIN_EMAIL"),
                    "total_steps": summary.get("totalSteps"),
                    "total_distance_m": summary.get("totalDistance"),
                    "calories_total": summary.get("totalKilocalories"),
                    "calories_active": summary.get("activeKilocalories"),
                    "sleep_duration_s": summary.get("sleepDurationInSeconds"),
                    "floors_climbed": summary.get("floorsClimbed"),
                    "min_hr": summary.get("minHeartRate"),
                    "max_hr": summary.get("maxHeartRate"),
                    "avg_stress_level": summary.get("averageStressLevel", -1),
                    "body_battery_max": summary.get("maxBodyBattery"),
                    "raw_data": json.dumps(summary)
                }
                sql = """
                INSERT INTO daily_summaries (summary_date, user_id, total_steps, total_distance_m, calories_total, calories_active, sleep_duration_s, floors_climbed, min_hr, max_hr, avg_stress_level, body_battery_max, raw_data)
                VALUES (%(summary_date)s, %(user_id)s, %(total_steps)s, %(total_distance_m)s, %(calories_total)s, %(calories_active)s, %(sleep_duration_s)s, %(floors_climbed)s, %(min_hr)s, %(max_hr)s, %(avg_stress_level)s, %(body_battery_max)s, %(raw_data)s)
                ON CONFLICT (summary_date) DO UPDATE SET
                    total_steps = EXCLUDED.total_steps, total_distance_m = EXCLUDED.total_distance_m, calories_total = EXCLUDED.calories_total,
                    calories_active = EXCLUDED.calories_active, sleep_duration_s = EXCLUDED.sleep_duration_s, floors_climbed = EXCLUDED.floors_climbed,
                    min_hr = EXCLUDED.min_hr, max_hr = EXCLUDED.max_hr, avg_stress_level = EXCLUDED.avg_stress_level,
                    body_battery_max = EXCLUDED.body_battery_max, raw_data = EXCLUDED.raw_data, processed_at = NOW();
                """
                cursor.execute(sql, data_to_insert)
                synced_count += 1
                    
            except Exception as e:
                logging.warning(f"Could not fetch summary for {current_date.isoformat()}: {e}")
            
            current_date += timedelta(days=1)
        
        conn.commit()
        logging.info(f"Successfully synced {synced_count} daily summaries.")

# Sync Sleep Data
def sync_sleep_data(client, conn):
    logging.info("--- Starting Sleep Data Sync ---")
    with conn.cursor() as cursor:
        cursor.execute("SELECT MAX(sleep_date) FROM sleep_data;")
        result = cursor.fetchone()
        start_date = result[0] + timedelta(days=1) if result and result[0] else date.today() - timedelta(days=30)
        end_date = date.today() - timedelta(days=1)

        if start_date > end_date:
            logging.info("Sleep data is already up-to-date.")
            return

        logging.info(f"Fetching sleep data from {start_date} to {end_date}")
        
        synced_count = 0
        current_date = start_date
        while current_date <= end_date:
            try:
                sleep_data = client.get_sleep_data(current_date.isoformat())
                if not sleep_data or not sleep_data.get('dailySleepDTO'):
                    current_date += timedelta(days=1)
                    continue

                sleep_dto = sleep_data['dailySleepDTO']
                
                if sleep_dto.get("sleepTimeSeconds") is None:
                    current_date += timedelta(days=1)
                    continue

                data_to_insert = {
                    "sleep_date": sleep_dto.get("calendarDate"),
                    "user_id": os.getenv("GARMIN_EMAIL"),
                    "sleep_start_time": sleep_dto.get("sleepStartTimestampGMT"),
                    "sleep_end_time": sleep_dto.get("sleepEndTimestampGMT"),
                    "total_sleep_time_s": sleep_dto.get("sleepTimeSeconds"),
                    "deep_sleep_s": sleep_dto.get("deepSleepSeconds"),
                    "light_sleep_s": sleep_dto.get("lightSleepSeconds"),
                    "rem_sleep_s": sleep_dto.get("remSleepSeconds"),
                    "awake_time_s": sleep_dto.get("awakeSleepSeconds"),
                    "sleep_score": None,
                    "sleep_efficiency": None,
                    "raw_data": json.dumps(sleep_data)
                }
                sql = """
                INSERT INTO sleep_data (sleep_date, user_id, sleep_start_time, sleep_end_time, total_sleep_time_s, deep_sleep_s, light_sleep_s, rem_sleep_s, awake_time_s, sleep_score, sleep_efficiency, raw_data)
                VALUES (%(sleep_date)s, %(user_id)s, %(sleep_start_time)s, %(sleep_end_time)s, %(total_sleep_time_s)s, %(deep_sleep_s)s, %(light_sleep_s)s, %(rem_sleep_s)s, %(awake_time_s)s, %(sleep_score)s, %(sleep_efficiency)s, %(raw_data)s)
                ON CONFLICT (sleep_date) DO UPDATE SET
                    user_id = EXCLUDED.user_id, sleep_start_time = EXCLUDED.sleep_start_time, sleep_end_time = EXCLUDED.sleep_end_time,
                    total_sleep_time_s = EXCLUDED.total_sleep_time_s, deep_sleep_s = EXCLUDED.deep_sleep_s, light_sleep_s = EXCLUDED.light_sleep_s,
                    rem_sleep_s = EXCLUDED.rem_sleep_s, awake_time_s = EXCLUDED.awake_time_s, raw_data = EXCLUDED.raw_data, processed_at = NOW();
                """
                cursor.execute(sql, data_to_insert)
                synced_count += 1
            except Exception as e:
                logging.warning(f"Could not fetch sleep data for {current_date.isoformat()}: {e}")
            
            current_date += timedelta(days=1)
        
        conn.commit()
        logging.info(f"Successfully synced {synced_count} sleep data records.")

# Sync Body Composition
def sync_body_composition(client, conn):
    logging.info("--- Starting Body Composition Sync ---")
    with conn.cursor() as cursor:
        cursor.execute("SELECT MAX(measurement_date) FROM body_composition;")
        result = cursor.fetchone()
        start_date = result[0] + timedelta(days=1) if result and result[0] else date.today() - timedelta(days=30)
        end_date = date.today() - timedelta(days=1)

        if start_date > end_date:
            logging.info("Body composition data is already up-to-date.")
            return

        logging.info(f"Fetching body composition data from {start_date} to {end_date}")
        
        synced_count = 0
        current_date = start_date
        while current_date <= end_date:
            try:
                body_data = client.get_body_composition(current_date.isoformat())
                if not body_data:
                    current_date += timedelta(days=1)
                    continue

                date_weights = body_data.get('dateWeightList', [])
                if not date_weights:
                    current_date += timedelta(days=1)
                    continue

                latest_measurement = date_weights[-1]
                data_to_insert = {
                    "measurement_date": latest_measurement.get("date"),
                    "user_id": os.getenv("GARMIN_EMAIL"),
                    "weight_kg": latest_measurement.get("weight"),
                    "body_fat_percentage": latest_measurement.get("bodyFat"),
                    "muscle_mass_kg": latest_measurement.get("muscleMass"),
                    "bone_mass_kg": latest_measurement.get("boneMass"),
                    "body_water_percentage": latest_measurement.get("bodyWater"),
                    "bmi": latest_measurement.get("bmi"),
                    "metabolic_age": latest_measurement.get("metabolicAge"),
                    "raw_data": json.dumps(body_data)
                }
                sql = """
                INSERT INTO body_composition (measurement_date, user_id, weight_kg, body_fat_percentage, muscle_mass_kg, bone_mass_kg, body_water_percentage, bmi, metabolic_age, raw_data)
                VALUES (%(measurement_date)s, %(user_id)s, %(weight_kg)s, %(body_fat_percentage)s, %(muscle_mass_kg)s, %(bone_mass_kg)s, %(body_water_percentage)s, %(bmi)s, %(metabolic_age)s, %(raw_data)s)
                ON CONFLICT (measurement_date) DO UPDATE SET
                    user_id = EXCLUDED.user_id, weight_kg = EXCLUDED.weight_kg, body_fat_percentage = EXCLUDED.body_fat_percentage,
                    muscle_mass_kg = EXCLUDED.muscle_mass_kg, bone_mass_kg = EXCLUDED.bone_mass_kg, body_water_percentage = EXCLUDED.body_water_percentage,
                    bmi = EXCLUDED.bmi, metabolic_age = EXCLUDED.metabolic_age, raw_data = EXCLUDED.raw_data, processed_at = NOW();
                """
                cursor.execute(sql, data_to_insert)
                synced_count += 1
            except Exception as e:
                logging.warning(f"Could not fetch body composition for {current_date.isoformat()}: {e}")
            
            current_date += timedelta(days=1)
        
        conn.commit()
        logging.info(f"Successfully synced {synced_count} body composition records.")

# Sync Training Metrics
def sync_training_metrics(client, conn):
    logging.info("--- Starting Training Metrics Sync ---")
    with conn.cursor() as cursor:
        cursor.execute("SELECT MAX(measurement_date::date) FROM training_metrics;")
        result = cursor.fetchone()
        start_date = result[0] + timedelta(days=1) if result and result[0] else date.today() - timedelta(days=30)
        end_date = date.today() - timedelta(days=1)

        if start_date > end_date:
            logging.info("Training metrics are already up-to-date.")
            return

        logging.info(f"Fetching training metrics from {start_date} to {end_date}")
        
        synced_count = 0
        current_date = start_date
        while current_date <= end_date:
            try:
                training_status = client.get_training_status(current_date.isoformat())
                if not training_status:
                    current_date += timedelta(days=1)
                    continue

                vo2_max = None
                fitness_age = None
                if training_status.get("mostRecentVO2Max"):
                    vo2_max_data = training_status["mostRecentVO2Max"].get("generic", {})
                    vo2_max = vo2_max_data.get("vo2MaxPreciseValue")
                    fitness_age = vo2_max_data.get("fitnessAge")

                data_to_insert = {
                    "measurement_date": current_date.isoformat(),
                    "user_id": os.getenv("GARMIN_EMAIL"),
                    "vo2_max": vo2_max,
                    "fitness_age": fitness_age,
                    "training_load_7_day": None,
                    "training_load_4_week": None,
                    "training_status": None,
                    "training_readiness_score": None,
                    "race_pred_5k_seconds": None,
                    "race_pred_10k_seconds": None,
                    "race_pred_half_marathon_seconds": None,
                    "race_pred_marathon_seconds": None,
                    "raw_data": json.dumps(training_status)
                }
                sql = """
                INSERT INTO training_metrics (measurement_date, user_id, vo2_max, fitness_age, training_load_7_day, training_load_4_week, training_status, training_readiness_score, race_pred_5k_seconds, race_pred_10k_seconds, race_pred_half_marathon_seconds, race_pred_marathon_seconds, raw_data)
                VALUES (%(measurement_date)s, %(user_id)s, %(vo2_max)s, %(fitness_age)s, %(training_load_7_day)s, %(training_load_4_week)s, %(training_status)s, %(training_readiness_score)s, %(race_pred_5k_seconds)s, %(race_pred_10k_seconds)s, %(race_pred_half_marathon_seconds)s, %(race_pred_marathon_seconds)s, %(raw_data)s)
                ON CONFLICT (measurement_date) DO UPDATE SET
                    user_id = EXCLUDED.user_id, vo2_max = EXCLUDED.vo2_max, fitness_age = EXCLUDED.fitness_age,
                    raw_data = EXCLUDED.raw_data, processed_at = NOW();
                """
                cursor.execute(sql, data_to_insert)
                synced_count += 1
            except Exception as e:
                logging.warning(f"Could not fetch training metrics for {current_date.isoformat()}: {e}")
            
            current_date += timedelta(days=1)
        
        conn.commit()
        logging.info(f"Successfully synced {synced_count} training metrics records.")

# Main Execution
if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Garmin Connect Data Sync Tool')
    parser.add_argument('--historical-days', type=int, default=0, 
                       help='Number of days of historical data to fetch (0 for regular sync mode)')
    parser.add_argument('--continuous', action='store_true', default=False,
                       help='Run in continuous sync mode (default for regular sync)')
    parser.add_argument('--sync-interval', type=int, default=24,
                       help='Hours between sync cycles in continuous mode (default: 24)')
    
    args = parser.parse_args()
    
    # Determine sync mode
    historical_mode = args.historical_days > 0
    continuous_mode = args.continuous or not historical_mode
    sync_interval_hours = args.sync_interval
    
    if historical_mode:
        logging.info(f"=== HISTORICAL SYNC MODE: Fetching {args.historical_days} days of data ===")
    else:
        logging.info(f"=== REGULAR SYNC MODE: Continuous sync every {sync_interval_hours} hours ===")
    
    # Initialize database
    db_conn_init = get_db_connection()
    if db_conn_init:
        init_db(db_conn_init)
        db_conn_init.close()
    else:
        logging.critical("Initial DB connection failed. Exiting.")
        exit(1)

    # Main sync logic
    def run_sync_cycle(is_historical=False):
        """Run a single sync cycle"""
        cycle_type = "Historical" if is_historical else "Regular"
        logging.info(f"=== Starting {cycle_type} Sync Cycle ===")
        
        conn = get_db_connection()
        if not conn:
            logging.error("Could not establish database connection. Aborting sync cycle.")
            return False
        
        try:
            client = Garmin(os.getenv("GARMIN_EMAIL"), os.getenv("GARMIN_PASSWORD"))
            logging.info("Attempting to login to Garmin Connect...")
            client.login()
            logging.info("Successfully logged in to Garmin Connect.")

            if is_historical:
                # Historical sync mode - only sync daily summaries with extended range
                logging.info("Running historical data sync...")
                synced_count = sync_daily_summaries_historical(client, conn, args.historical_days)
                logging.info(f"Historical sync completed: {synced_count} records synced")
            else:
                # Regular sync mode - run all sync functions
                # Initialize smart sync manager
                smart_sync = SmartSyncManager(conn)
                
                # Run data integrity validation before sync
                logging.info("Running pre-sync data integrity validation...")
                integrity_report = validate_data_integrity(conn, 'daily_summaries')
                if integrity_report['issues']:
                    logging.warning(f"Data integrity issues found: {integrity_report['issues']}")
                
                # Clean up stale data
                smart_sync.clean_stale_data()
                
                # Run all sync functions with smart sync wrapper
                smart_sync.sync_with_validation(sync_activities, 'activities', client, conn)
                smart_sync.sync_with_validation(sync_daily_summaries, 'daily_summaries', client, conn)
                smart_sync.sync_with_validation(sync_sleep_data, 'sleep_data', client, conn)
                smart_sync.sync_with_validation(sync_body_composition, 'body_composition', client, conn)
                smart_sync.sync_with_validation(sync_training_metrics, 'training_metrics', client, conn)
                
                # Get and log sync statistics
                stats = smart_sync.get_sync_statistics()
                logging.info(f"Sync statistics: {json.dumps(stats, indent=2)}")

            return True

        except Exception as e:
            logging.error(f"An error occurred in the {cycle_type.lower()} sync cycle: ")
            logging.error(f"Error type: {type(e).__name__}")
            logging.error(f"Error message: {str(e)}")
            logging.error("Traceback:", exc_info=True)
            
            # Check if it's an authentication issue
            if "login" in str(e).lower() or "auth" in str(e).lower() or "AssertionError" in str(e):
                logging.error("Authentication failed. Please check your Garmin credentials.")
                logging.error("This may be due to Garmin API changes or incorrect credentials.")
            
            return False
        finally:
            if conn:
                conn.close()

    # Execute sync based on mode
    if historical_mode:
        # Run historical sync once and exit
        success = run_sync_cycle(is_historical=True)
        if success:
            logging.info("=== Historical Sync Complete ===")
            logging.info(f"Successfully fetched {args.historical_days} days of historical data.")
        else:
            logging.error("=== Historical Sync Failed ===")
        exit(0 if success else 1)
    else:
        # Run continuous sync mode
        while True:
            success = run_sync_cycle(is_historical=False)
            
            if success:
                logging.info(f"=== Sync Cycle Complete. Sleeping for {sync_interval_hours} hours. ===")
            else:
                logging.info("Sync cycle failed. Will retry in next cycle.")
            
            time.sleep(sync_interval_hours * 3600)