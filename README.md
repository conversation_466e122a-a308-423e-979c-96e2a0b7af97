# WearLink - Universal Wearable Device Platform

A comprehensive fitness and health data platform that connects multiple wearable devices into a unified dashboard.

## Supported Devices

### Currently Supported
- **Garmin Devices** - Full integration with Garmin Connect API
  - Activities and workouts
  - Daily summaries (steps, calories, heart rate)
  - Sleep analysis
  - Training metrics (VO2 Max, fitness age)
  - Body composition and health metrics

### Planned Support
- **Apple Watch** - Apple Health integration
- **Oura Ring** - Sleep and recovery metrics
- **Amazfit Devices** - Multi-device ecosystem support

## Architecture

WearLink is built as a scalable platform that can accommodate multiple device types:

- **Universal Data Layer** - Normalized health metrics across all devices
- **Device-Specific Services** - Individual sync services for each device type
- **Unified Dashboard** - Single interface for all your wearable data
- **Extensible API** - Easy integration of new device types

## Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd wearlink

# Copy environment template
cp .env.example .env

# Fill in your device credentials
nano .env

# Start the platform
docker compose up -d

# Access the dashboard
open http://localhost:3001
```

## Technology Stack

- **Backend**: Python (data sync), Node.js (API)
- **Frontend**: Next.js with TypeScript
- **Database**: PostgreSQL
- **Infrastructure**: Docker Compose
- **UI**: React with Tailwind CSS

## Contributing

WearLink is designed to be device-agnostic. Each device integration follows the same pattern:

1. Device-specific sync service
2. Normalized data structure
3. API endpoint integration
4. Dashboard component

See the development guide for adding new device support.

## License

MIT License - See LICENSE file for details.