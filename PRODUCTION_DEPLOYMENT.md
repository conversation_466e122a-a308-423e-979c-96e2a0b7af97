# WearLink Production Deployment Guide

## 🚀 Production Deployment Checklist

### Prerequisites
- [ ] Docker and Docker Compose installed
- [ ] SSL certificates obtained (Let's Encrypt recommended)
- [ ] Domain name configured
- [ ] Firewall configured (ports 80, 443, optionally 3001)
- [ ] Backup storage configured (S3 or local)

### Security Setup

#### 1. Environment Configuration
```bash
# Copy production environment template
cp .env.production .env

# Generate secure secrets
openssl rand -hex 32 > session_secret.txt
openssl rand -hex 64 > jwt_secret.txt

# Update .env with your values
nano .env
```

#### 2. SSL Certificate Setup
```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# Update .env with certificate paths
SSL_CERT_PATH=/etc/letsencrypt/live/your-domain.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/your-domain.com/privkey.pem
```

#### 3. Database Security
```bash
# Generate secure database password
openssl rand -base64 32

# Update POSTGRES_PASSWORD in .env
```

### Production Deployment

#### 1. Initial Deployment
```bash
# Build and start production services
docker compose -f docker-compose.prod.yml up -d

# Check service status
docker compose -f docker-compose.prod.yml ps

# View logs
docker compose -f docker-compose.prod.yml logs -f
```

#### 2. Enable Monitoring (Optional)
```bash
# Start with monitoring services
docker compose -f docker-compose.prod.yml --profile monitoring up -d

# Access Grafana at http://localhost:3000
# Default credentials: admin / [GRAFANA_PASSWORD from .env]
```

#### 3. Enable Redis Cache (Optional)
```bash
# Start with cache services
docker compose -f docker-compose.prod.yml --profile cache up -d
```

### Backup Configuration

#### 1. Setup Automated Backups
```bash
# Make backup script executable
chmod +x scripts/backup.sh
chmod +x scripts/restore.sh

# Configure S3 backup (optional)
aws configure

# Test backup
./scripts/backup.sh

# Setup cron job for automated backups
crontab -e
# Add: 0 2 * * * /path/to/wearlink/scripts/backup.sh
```

#### 2. Backup Retention
- Local backups: 7 days (configurable via BACKUP_RETENTION_DAYS)
- S3 backups: 30 days (configurable via BACKUP_RETENTION_DAYS)

### Monitoring & Logging

#### 1. Application Logs
```bash
# Application logs location
/var/log/wearlink/

# View live logs
docker compose -f docker-compose.prod.yml logs -f wearlink-frontend
docker compose -f docker-compose.prod.yml logs -f garmin-data-sync
```

#### 2. Health Checks
```bash
# Check application health
curl http://localhost:3001/api/health

# Check sync analytics
curl http://localhost:3001/api/sync/analytics
```

#### 3. Database Monitoring
```bash
# Connect to database
docker compose -f docker-compose.prod.yml exec postgres psql -U wearlink_production_user -d wearlink_production

# Check database performance
SELECT * FROM pg_stat_activity;
SELECT * FROM pg_stat_database;
```

### Performance Optimization

#### 1. Database Tuning
- Configured PostgreSQL settings in docker-compose.prod.yml
- Connection pooling enabled
- Performance monitoring queries included

#### 2. Application Caching
- Redis cache available (enable with --profile cache)
- API response caching configured
- Static file caching enabled

#### 3. Resource Limits
- Memory and CPU limits configured for all services
- Health checks with appropriate timeouts
- Restart policies configured

### Security Hardening

#### 1. Network Security
- Database bound to localhost only
- Internal Docker network isolation
- Non-root users for all services

#### 2. Access Control
- Rate limiting configured
- CORS properly configured
- Session management with secure secrets

#### 3. SSL/TLS
- HTTPS enforcement
- Secure headers configured
- Certificate auto-renewal with Let's Encrypt

### Maintenance Tasks

#### 1. Regular Updates
```bash
# Update application
git pull origin main
docker compose -f docker-compose.prod.yml build --no-cache
docker compose -f docker-compose.prod.yml up -d

# Update base images
docker compose -f docker-compose.prod.yml pull
docker compose -f docker-compose.prod.yml up -d
```

#### 2. Database Maintenance
```bash
# Vacuum and analyze database
docker compose -f docker-compose.prod.yml exec postgres psql -U wearlink_production_user -d wearlink_production -c "VACUUM ANALYZE;"

# Check database size
docker compose -f docker-compose.prod.yml exec postgres psql -U wearlink_production_user -d wearlink_production -c "SELECT pg_size_pretty(pg_database_size('wearlink_production'));"
```

#### 3. Log Rotation
```bash
# Configure logrotate
sudo nano /etc/logrotate.d/wearlink

# Add configuration:
/var/log/wearlink/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
```

### Disaster Recovery

#### 1. Database Restore
```bash
# Restore from backup
./scripts/restore.sh /path/to/backup.sql.gz

# Restore from S3
aws s3 cp s3://your-backup-bucket/database/backup.sql.gz ./
./scripts/restore.sh backup.sql.gz
```

#### 2. Full System Recovery
```bash
# Restore from complete backup
git clone https://github.com/yourusername/wearlink.git
cd wearlink
cp /backup/location/.env .
docker compose -f docker-compose.prod.yml up -d
./scripts/restore.sh /backup/location/database_backup.sql.gz
```

### Troubleshooting

#### Common Issues

1. **Service Won't Start**
   ```bash
   # Check logs
   docker compose -f docker-compose.prod.yml logs [service-name]
   
   # Check resource usage
   docker stats
   ```

2. **Database Connection Issues**
   ```bash
   # Test database connectivity
   docker compose -f docker-compose.prod.yml exec postgres pg_isready
   
   # Check database logs
   docker compose -f docker-compose.prod.yml logs postgres
   ```

3. **Sync Service Issues**
   ```bash
   # Check Garmin credentials
   docker compose -f docker-compose.prod.yml logs garmin-data-sync
   
   # Manual sync test
   docker compose -f docker-compose.prod.yml exec garmin-data-sync python -c "from garminconnect import Garmin; print('OK')"
   ```

### Support & Monitoring

#### Metrics Endpoints
- Application Health: `http://localhost:3001/api/health`
- Sync Analytics: `http://localhost:3001/api/sync/analytics`
- Prometheus Metrics: `http://localhost:9090` (if monitoring enabled)
- Grafana Dashboard: `http://localhost:3000` (if monitoring enabled)

#### Alert Thresholds
- Database connection failures
- Sync failures (> 24 hours)
- High memory usage (> 80%)
- Disk space (< 10% free)
- SSL certificate expiration (< 30 days)

For additional support, check the troubleshooting section in the main README or create an issue in the repository.