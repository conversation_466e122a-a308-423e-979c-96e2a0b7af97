# WearLink - Universal Wearable Device Platform
# Environment Configuration Template

# Database Configuration
POSTGRES_DB=wearlink_data
POSTGRES_USER=wearlink_user
POSTGRES_PASSWORD=your_super_strong_password

# Garmin Connect Credentials
GARMIN_EMAIL=<EMAIL>
GARMIN_PASSWORD=your_garmin_password

# Apple Health API Configuration (Future)
APPLE_HEALTH_API_KEY=your_apple_health_api_key
APPLE_HEALTH_CLIENT_ID=your_apple_client_id

# Oura Ring API Configuration (Future)
OURA_API_TOKEN=your_oura_api_token
OURA_CLIENT_ID=your_oura_client_id

# Amazfit API Configuration (Future)
AMAZFIT_API_KEY=your_amazfit_api_key
AMAZFIT_CLIENT_SECRET=your_amazfit_client_secret

# Application Settings
NODE_ENV=production
LOG_LEVEL=info

# Security
SESSION_SECRET=your_session_secret_key_here
JWT_SECRET=your_jwt_secret_key_here