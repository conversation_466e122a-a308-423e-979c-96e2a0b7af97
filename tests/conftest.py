"""
Pytest configuration and fixtures for WearLink integration tests
"""

import pytest
import psycopg2
import os
from datetime import date, timedelta
import json
from unittest.mock import MagicMock

# Test database configuration
TEST_DB_CONFIG = {
    'host': os.getenv('TEST_DB_HOST', 'localhost'),
    'database': os.getenv('TEST_DB_NAME', 'wearlink_test'),
    'user': os.getenv('TEST_DB_USER', 'wearlink_user'),
    'password': os.getenv('TEST_DB_PASSWORD', 'test_password'),
    'port': os.getenv('TEST_DB_PORT', '5433')
}

@pytest.fixture(scope="session")
def test_db_connection():
    """Create a test database connection for the session"""
    conn = psycopg2.connect(**TEST_DB_CONFIG)
    yield conn
    conn.close()

@pytest.fixture(scope="function")
def clean_test_db(test_db_connection):
    """Clean test database before each test"""
    with test_db_connection.cursor() as cursor:
        # Clean all tables
        cursor.execute("TRUNCATE TABLE activities, daily_summaries, sleep_data, hrv_data, body_composition, spo2_data, training_metrics RESTART IDENTITY CASCADE;")
        test_db_connection.commit()
    yield test_db_connection
    
@pytest.fixture
def sample_activity_data():
    """Sample Garmin activity data for testing"""
    return {
        'activityId': 12345678901,
        'activityType': {'typeKey': 'running'},
        'startTimeGMT': '2025-07-26T06:00:00.000',
        'distance': 5000.0,  # meters
        'duration': 1800.0,  # 30 minutes
        'averageHR': 150,
        'maxHR': 180,
        'calories': 350
    }

@pytest.fixture
def sample_daily_summary():
    """Sample daily summary data for testing"""
    return {
        'summaryDate': '2025-07-26',
        'totalSteps': 8500,
        'totalDistance': 6200,  # meters
        'activeCalories': 450,
        'totalCalories': 2100,
        'sleepingSeconds': 28800,  # 8 hours
        'floorsClimbed': 12,
        'minHeartRate': 52,
        'maxHeartRate': 165,
        'averageStressLevel': 35,
        'maxBodyBattery': 85
    }

@pytest.fixture
def mock_garmin_client():
    """Mock Garmin Connect client for testing"""
    client = MagicMock()
    client.login.return_value = True
    return client

@pytest.fixture
def test_date_range():
    """Test date range for integration tests"""
    end_date = date.today() - timedelta(days=1)
    start_date = end_date - timedelta(days=7)
    return start_date, end_date