"""
API integration tests for WearLink platform
Tests API endpoints, response formats, and error handling
"""

import pytest
import requests
import json
from datetime import date, datetime, timedelta
import time


class TestAPIIntegration:
    """Test API endpoint integration"""
    
    BASE_URL = "http://localhost:3001/api"
    
    @pytest.fixture(autouse=True)
    def setup_api_test(self):
        """Setup for API tests - ensure service is running"""
        # Wait for service to be ready
        max_retries = 30
        for i in range(max_retries):
            try:
                response = requests.get(f"{self.BASE_URL}/health", timeout=5)
                if response.status_code == 200:
                    break
            except requests.RequestException:
                if i == max_retries - 1:
                    pytest.skip("API service not available for testing")
                time.sleep(1)

    def test_health_endpoint(self):
        """Test health check endpoint"""
        response = requests.get(f"{self.BASE_URL}/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data['success'] is True
        assert data['status'] == 'healthy'
        assert 'timestamp' in data
        assert 'database' in data
        assert data['database']['connected'] is True

    def test_stats_overview_endpoint(self):
        """Test stats overview endpoint"""
        response = requests.get(f"{self.BASE_URL}/stats/overview")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data['success'] is True
        assert 'data' in data
        assert 'meta' in data
        
        # Check required fields in data
        required_fields = ['totalActivities', 'avgDailySteps', 'currentWeekCalories', 'trends']
        for field in required_fields:
            assert field in data['data']
        
        # Check trends structure
        assert 'stepsChange' in data['data']['trends']
        assert 'caloriesChange' in data['data']['trends']

    def test_daily_summaries_endpoint(self):
        """Test daily summaries endpoint"""
        response = requests.get(f"{self.BASE_URL}/daily-summaries")
        
        # Should return success even if no data
        assert response.status_code == 200
        data = response.json()
        
        if data['success']:
            assert 'data' in data
            assert isinstance(data['data'], list)
        else:
            # If failed, should have error message
            assert 'error' in data

    def test_sync_status_endpoint(self):
        """Test sync status endpoint"""
        response = requests.get(f"{self.BASE_URL}/sync/status")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data['success'] is True
        assert 'data' in data
        assert 'meta' in data
        
        # Check sync status fields
        status_data = data['data']
        required_fields = [
            'service_status', 'total_activities', 'total_summaries',
            'recent_sync_stats'
        ]
        for field in required_fields:
            assert field in status_data

    def test_sync_analytics_endpoint(self):
        """Test new sync analytics endpoint"""
        response = requests.get(f"{self.BASE_URL}/sync/analytics")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data['success'] is True
        assert 'data' in data
        
        # Check analytics structure
        analytics_data = data['data']
        assert 'sync_statistics' in analytics_data
        assert 'data_quality' in analytics_data
        assert 'health_indicators' in analytics_data
        
        # Check data quality metrics
        quality_data = analytics_data['data_quality']
        assert 'completeness_score' in quality_data
        assert 'quality_score' in quality_data
        assert isinstance(quality_data['completeness_score'], int)
        assert isinstance(quality_data['quality_score'], int)

    def test_error_handling(self):
        """Test API error handling for non-existent endpoints"""
        response = requests.get(f"{self.BASE_URL}/nonexistent")
        
        assert response.status_code == 404

    def test_api_response_format(self):
        """Test consistent API response format across endpoints"""
        endpoints = [
            "/health",
            "/stats/overview", 
            "/sync/status",
            "/sync/analytics"
        ]
        
        for endpoint in endpoints:
            response = requests.get(f"{self.BASE_URL}{endpoint}")
            assert response.status_code == 200
            
            data = response.json()
            # All successful responses should have 'success' field
            assert 'success' in data
            assert data['success'] is True
            
            # Should have either 'data' or direct response data
            assert 'data' in data or 'status' in data

    def test_api_content_type(self):
        """Test API returns correct content type"""
        response = requests.get(f"{self.BASE_URL}/health")
        
        assert response.status_code == 200
        assert 'application/json' in response.headers.get('content-type', '')

    def test_api_performance(self):
        """Test API response times are reasonable"""
        start_time = time.time()
        response = requests.get(f"{self.BASE_URL}/stats/overview")
        end_time = time.time()
        
        assert response.status_code == 200
        response_time = end_time - start_time
        
        # API should respond within 5 seconds
        assert response_time < 5.0, f"API response took {response_time:.2f} seconds"


class TestDockerIntegration:
    """Test Docker compose setup and service integration"""
    
    def test_service_connectivity(self):
        """Test that all services can communicate"""
        # Test frontend can reach database through API
        response = requests.get("http://localhost:3001/api/health")
        
        assert response.status_code == 200
        data = response.json()
        
        # Should indicate successful database connection
        assert data['database']['connected'] is True
        assert 'server_time' in data['database']
        assert 'version' in data['database']

    def test_service_health_checks(self):
        """Test Docker health checks are working"""
        import subprocess
        
        # Check container health status
        result = subprocess.run([
            'docker', 'compose', 'ps', '--format', 'json'
        ], capture_output=True, text=True, cwd='/home/<USER>/wearlink')
        
        if result.returncode == 0:
            services = json.loads(result.stdout) if result.stdout.strip() else []
            
            for service in services:
                # All services should be running
                assert 'running' in service.get('State', '').lower()
        else:
            pytest.skip("Docker compose not available for testing")

    def test_data_persistence(self):
        """Test that data persists across API calls"""
        # Make multiple calls to stats endpoint
        response1 = requests.get("http://localhost:3001/api/stats/overview")
        response2 = requests.get("http://localhost:3001/api/stats/overview")
        
        assert response1.status_code == 200
        assert response2.status_code == 200
        
        data1 = response1.json()
        data2 = response2.json()
        
        # Total activities should be consistent
        assert data1['data']['totalActivities'] == data2['data']['totalActivities']