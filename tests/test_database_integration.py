"""
Database integration tests for WearLink platform
Tests database operations, schema integrity, and data validation
"""

import pytest
import json
from datetime import date, datetime, timedelta
from data_sync.smart_sync_utils import validate_data_integrity, SmartSyncManager


class TestDatabaseIntegration:
    """Test database operations and data integrity"""

    def test_database_connection(self, test_db_connection):
        """Test basic database connectivity"""
        with test_db_connection.cursor() as cursor:
            cursor.execute("SELECT 1;")
            result = cursor.fetchone()
            assert result[0] == 1

    def test_table_schema_exists(self, test_db_connection):
        """Test that all required tables exist with correct schema"""
        expected_tables = [
            'activities', 'daily_summaries', 'sleep_data', 
            'hrv_data', 'body_composition', 'spo2_data', 'training_metrics'
        ]
        
        with test_db_connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name;
            """)
            actual_tables = [row[0] for row in cursor.fetchall()]
            
        for table in expected_tables:
            assert table in actual_tables, f"Table {table} not found in database"

    def test_daily_summary_insert(self, clean_test_db, sample_daily_summary):
        """Test inserting daily summary data"""
        with clean_test_db.cursor() as cursor:
            sql = """
            INSERT INTO daily_summaries 
            (summary_date, user_id, total_steps, total_distance_m, calories_active, calories_total, 
             sleep_duration_s, floors_climbed, min_hr, max_hr, avg_stress_level, body_battery_max, raw_data)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            data = (
                sample_daily_summary['summaryDate'],
                '<EMAIL>',
                sample_daily_summary['totalSteps'],
                sample_daily_summary['totalDistance'],
                sample_daily_summary['activeCalories'],
                sample_daily_summary['totalCalories'],
                sample_daily_summary['sleepingSeconds'],
                sample_daily_summary['floorsClimbed'],
                sample_daily_summary['minHeartRate'],
                sample_daily_summary['maxHeartRate'],
                sample_daily_summary['averageStressLevel'],
                sample_daily_summary['maxBodyBattery'],
                json.dumps(sample_daily_summary)
            )
            
            cursor.execute(sql, data)
            clean_test_db.commit()
            
            # Verify insertion
            cursor.execute("SELECT COUNT(*) FROM daily_summaries;")
            assert cursor.fetchone()[0] == 1

    def test_activity_insert(self, clean_test_db, sample_activity_data):
        """Test inserting activity data"""
        with clean_test_db.cursor() as cursor:
            sql = """
            INSERT INTO activities 
            (activity_id, user_id, activity_type, start_time_gmt, distance_km, 
             duration_s, average_hr, max_hr, calories, raw_data)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            data = (
                sample_activity_data['activityId'],
                '<EMAIL>',
                sample_activity_data['activityType']['typeKey'],
                sample_activity_data['startTimeGMT'],
                sample_activity_data['distance'] / 1000.0,  # Convert to km
                sample_activity_data['duration'],
                sample_activity_data['averageHR'],
                sample_activity_data['maxHR'],
                sample_activity_data['calories'],
                json.dumps(sample_activity_data)
            )
            
            cursor.execute(sql, data)
            clean_test_db.commit()
            
            # Verify insertion
            cursor.execute("SELECT COUNT(*) FROM activities;")
            assert cursor.fetchone()[0] == 1

    def test_data_integrity_validation(self, clean_test_db):
        """Test data integrity validation functions"""
        # Insert some test data with integrity issues
        with clean_test_db.cursor() as cursor:
            # Good record
            cursor.execute("""
                INSERT INTO daily_summaries (summary_date, user_id, total_steps, calories_total)
                VALUES (%s, %s, %s, %s)
            """, (date.today() - timedelta(days=1), '<EMAIL>', 8000, 2000))
            
            # Bad record - negative steps
            cursor.execute("""
                INSERT INTO daily_summaries (summary_date, user_id, total_steps, calories_total)
                VALUES (%s, %s, %s, %s)
            """, (date.today() - timedelta(days=2), '<EMAIL>', -100, 2000))
            
            # Bad record - impossible steps
            cursor.execute("""
                INSERT INTO daily_summaries (summary_date, user_id, total_steps, calories_total)
                VALUES (%s, %s, %s, %s)
            """, (date.today() - timedelta(days=3), '<EMAIL>', 150000, 2000))
            
            clean_test_db.commit()
        
        # Run integrity validation
        integrity_report = validate_data_integrity(clean_test_db, 'daily_summaries')
        
        assert integrity_report['table'] == 'daily_summaries'
        assert integrity_report['total_records'] == 3
        assert len(integrity_report['issues']) > 0
        assert integrity_report['valid_records'] == 1

    def test_smart_sync_manager(self, clean_test_db):
        """Test SmartSyncManager functionality"""
        manager = SmartSyncManager(clean_test_db)
        
        # Test sync statistics with empty database
        stats = manager.get_sync_statistics()
        assert 'daily_summaries' in stats
        assert 'activities' in stats
        assert stats['daily_summaries']['total_records'] == 0
        
        # Test optimal sync window calculation
        start_date, end_date = manager.get_optimal_sync_window('daily_summaries')
        assert isinstance(start_date, date)
        assert isinstance(end_date, date)
        assert start_date <= end_date

    def test_duplicate_handling(self, clean_test_db, sample_daily_summary):
        """Test handling of duplicate records"""
        # Insert same record twice
        with clean_test_db.cursor() as cursor:
            sql = """
            INSERT INTO daily_summaries 
            (summary_date, user_id, total_steps, calories_total, raw_data)
            VALUES (%s, %s, %s, %s, %s)
            ON CONFLICT (summary_date) DO UPDATE SET
                total_steps = EXCLUDED.total_steps,
                calories_total = EXCLUDED.calories_total,
                processed_at = NOW()
            """
            
            # First insert
            cursor.execute(sql, (
                sample_daily_summary['summaryDate'],
                '<EMAIL>',
                sample_daily_summary['totalSteps'],
                sample_daily_summary['totalCalories'],
                json.dumps(sample_daily_summary)
            ))
            
            # Second insert with different values
            cursor.execute(sql, (
                sample_daily_summary['summaryDate'],
                '<EMAIL>',
                sample_daily_summary['totalSteps'] + 1000,  # Different value
                sample_daily_summary['totalCalories'] + 100,
                json.dumps(sample_daily_summary)
            ))
            
            clean_test_db.commit()
            
            # Should have only one record with updated values
            cursor.execute("SELECT COUNT(*), total_steps FROM daily_summaries GROUP BY total_steps;")
            result = cursor.fetchone()
            assert result[0] == 1  # Only one record
            assert result[1] == sample_daily_summary['totalSteps'] + 1000  # Updated value

    def test_date_range_queries(self, clean_test_db):
        """Test date range queries for API endpoints"""
        # Insert test data for multiple days
        with clean_test_db.cursor() as cursor:
            for i in range(7):
                test_date = date.today() - timedelta(days=i+1)
                cursor.execute("""
                    INSERT INTO daily_summaries 
                    (summary_date, user_id, total_steps, calories_total)
                    VALUES (%s, %s, %s, %s)
                """, (test_date, '<EMAIL>', 8000 + i*100, 2000 + i*50))
            
            clean_test_db.commit()
            
            # Test last 30 days query (API endpoint simulation)
            cursor.execute("""
                SELECT COUNT(*), AVG(total_steps::float) 
                FROM daily_summaries 
                WHERE summary_date >= %s
            """, (date.today() - timedelta(days=30),))
            
            result = cursor.fetchone()
            assert result[0] == 7  # All 7 records
            assert result[1] > 8000  # Average steps


class TestAPIIntegration:
    """Test API endpoint integration with database"""
    
    def test_stats_overview_query(self, clean_test_db):
        """Test the query used by stats/overview API endpoint"""
        # Insert test data
        with clean_test_db.cursor() as cursor:
            # Activities
            cursor.execute("""
                INSERT INTO activities 
                (activity_id, user_id, activity_type, start_time_gmt, calories)
                VALUES (%s, %s, %s, %s, %s)
            """, (123456789, '<EMAIL>', 'running', datetime.now(), 300))
            
            # Daily summaries
            for i in range(5):
                test_date = date.today() - timedelta(days=i+1)
                cursor.execute("""
                    INSERT INTO daily_summaries 
                    (summary_date, user_id, total_steps, calories_total, avg_stress_level)
                    VALUES (%s, %s, %s, %s, %s)
                """, (test_date, '<EMAIL>', 8000 + i*200, 2000, 25 + i))
            
            clean_test_db.commit()
            
            # Test overview stats query
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_activities,
                    (SELECT COALESCE(AVG(total_steps), 0) FROM daily_summaries WHERE summary_date >= %s) as avg_daily_steps,
                    (SELECT COALESCE(SUM(calories_total), 0) FROM daily_summaries WHERE summary_date >= %s) as week_calories,
                    (SELECT avg_stress_level FROM daily_summaries ORDER BY summary_date DESC LIMIT 1) as latest_stress
                FROM activities
                WHERE start_time_gmt >= %s
            """, (
                date.today() - timedelta(days=30),
                date.today() - timedelta(days=7),
                datetime.now() - timedelta(days=30)
            ))
            
            result = cursor.fetchone()
            assert result[0] == 1  # Total activities
            assert result[1] > 0   # Average daily steps
            assert result[2] > 0   # Week calories
            assert result[3] >= 25 # Latest stress level