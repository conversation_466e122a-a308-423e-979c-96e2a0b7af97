#!/bin/bash

# WearLink Integration Test Runner
# Runs comprehensive integration tests for the platform

set -e

echo "🧪 Starting WearLink Integration Tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if main services are running
echo "📋 Checking service status..."
if ! docker compose ps | grep -q "healthy"; then
    echo -e "${YELLOW}Warning: Main services may not be running. Starting services...${NC}"
    docker compose up -d
    echo "Waiting for services to be ready..."
    sleep 30
fi

# Start test environment
echo "🚀 Starting test environment..."
docker compose -f docker-compose.test.yml up -d postgres-test

# Wait for test database
echo "⏳ Waiting for test database..."
sleep 10

# Run database integration tests
echo "🗄️  Running database integration tests..."
docker compose -f docker-compose.test.yml run --rm test-runner pytest tests/test_database_integration.py -v

# Run API integration tests (requires main services)
echo "🌐 Running API integration tests..."
docker compose -f docker-compose.test.yml run --rm test-runner pytest tests/test_api_integration.py -v

# Generate coverage report
echo "📊 Generating test coverage report..."
docker compose -f docker-compose.test.yml run --rm test-runner pytest tests/ --cov=data_sync --cov-report=html --cov-report=term

# Clean up test environment
echo "🧹 Cleaning up test environment..."
docker compose -f docker-compose.test.yml down -v

echo -e "${GREEN}✅ All integration tests completed successfully!${NC}"
echo "📈 Coverage report generated in htmlcov/ directory"