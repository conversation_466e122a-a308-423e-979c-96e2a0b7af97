version: '3.8'

services:
  # Test PostgreSQL Database
  postgres-test:
    image: postgres:15-alpine
    container_name: wearlink-postgres-test
    environment:
      POSTGRES_DB: wearlink_test
      POSTGRES_USER: wearlink_user
      POSTGRES_PASSWORD: test_password
    volumes:
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5434:5432"  # Different port to avoid conflicts
    networks:
      - wearlink_test_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U wearlink_user -d wearlink_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Test Runner
  test-runner:
    build:
      context: ./tests
      dockerfile: Dockerfile
    container_name: wearlink-test-runner
    environment:
      TEST_DB_HOST: postgres-test
      TEST_DB_NAME: wearlink_test
      TEST_DB_USER: wearlink_user
      TEST_DB_PASSWORD: test_password
      TEST_DB_PORT: 5432
    depends_on:
      postgres-test:
        condition: service_healthy
    networks:
      - wearlink_test_network
      - wearlink_fitness_network  # To access main services
    volumes:
      - ./tests:/app/tests
      - ./data-sync:/app/data_sync  # Mount source code for testing
    command: pytest /app/tests -v --tb=short

networks:
  wearlink_test_network:
    driver: bridge
  wearlink_fitness_network:
    external: true